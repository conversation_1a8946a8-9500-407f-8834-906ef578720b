const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const LockdownSystem = require('../utils/lockdownSystem');

// Initialize lockdown system
const lockdownSystem = new LockdownSystem();

module.exports = {
    data: new SlashCommandBuilder()
        .setName('lockdown')
        .setDescription('Advanced server lockdown system')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addSubcommand(subcommand =>
            subcommand
                .setName('on')
                .setDescription('Activate server lockdown')
                .addStringOption(option =>
                    option
                        .setName('reason')
                        .setDescription('Reason for the lockdown')
                        .setRequired(true)
                        .setMaxLength(500)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('off')
                .setDescription('Deactivate server lockdown')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('allow')
                .setDescription('Grant access to a user or role during lockdown')
                .addUserOption(option =>
                    option
                        .setName('user')
                        .setDescription('User to grant access to')
                        .setRequired(false)
                )
                .addRoleOption(option =>
                    option
                        .setName('role')
                        .setDescription('Role to grant access to')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('status')
                .setDescription('Check current lockdown status')
        ),

    async execute(interaction) {
        // Check permissions
        if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
            return await interaction.reply({
                content: '❌ You need Administrator permissions to use lockdown commands.',
                ephemeral: true
            });
        }

        // Check bot permissions
        const botMember = interaction.guild.members.me;
        const requiredPermissions = [
            PermissionFlagsBits.ManageChannels,
            PermissionFlagsBits.ManageRoles,
            PermissionFlagsBits.ViewChannel,
            PermissionFlagsBits.SendMessages
        ];

        const missingPermissions = requiredPermissions.filter(perm => !botMember.permissions.has(perm));
        if (missingPermissions.length > 0) {
            const permNames = missingPermissions.map(perm => 
                Object.keys(PermissionFlagsBits).find(key => PermissionFlagsBits[key] === perm)
            ).join(', ');
            
            return await interaction.reply({
                content: `❌ Bot is missing required permissions: ${permNames}`,
                ephemeral: true
            });
        }

        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'on':
                    await handleLockdownOn(interaction, lockdownSystem);
                    break;
                case 'off':
                    await handleLockdownOff(interaction, lockdownSystem);
                    break;
                case 'allow':
                    await handleLockdownAllow(interaction, lockdownSystem);
                    break;
                case 'status':
                    await handleLockdownStatus(interaction, lockdownSystem);
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Unknown subcommand.',
                        ephemeral: true
                    });
            }
        } catch (error) {
            console.error('Lockdown command error:', error);
            
            const errorMessage = '❌ An error occurred while executing the lockdown command.';
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            } else if (interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.followUp({ content: errorMessage, ephemeral: true });
            }
        }
    }
};

// Handle lockdown activation
async function handleLockdownOn(interaction, lockdownSystem) {
    const reason = interaction.options.getString('reason');
    
    // Validate reason
    if (reason.length < 5) {
        return await interaction.reply({
            content: '❌ Lockdown reason must be at least 5 characters long.',
            ephemeral: true
        });
    }

    await lockdownSystem.activateLockdown(interaction, reason);
}

// Handle lockdown deactivation
async function handleLockdownOff(interaction, lockdownSystem) {
    await lockdownSystem.deactivateLockdown(interaction);
}

// Handle granting access
async function handleLockdownAllow(interaction, lockdownSystem) {
    const user = interaction.options.getMember('user');
    const role = interaction.options.getRole('role');

    // Check if either user or role is provided
    if (!user && !role) {
        return await interaction.reply({
            content: '❌ You must specify either a user or a role to grant access to.',
            ephemeral: true
        });
    }

    // Check if both are provided
    if (user && role) {
        return await interaction.reply({
            content: '❌ You can only specify either a user OR a role, not both.',
            ephemeral: true
        });
    }

    const target = user || role;
    
    // Additional validation for roles
    if (role) {
        // Don't allow @everyone
        if (role.id === interaction.guild.roles.everyone.id) {
            return await interaction.reply({
                content: '❌ Cannot grant access to @everyone role.',
                ephemeral: true
            });
        }

        // Don't allow bot roles
        if (role.managed) {
            return await interaction.reply({
                content: '❌ Cannot grant access to managed/bot roles.',
                ephemeral: true
            });
        }
    }

    await lockdownSystem.allowAccess(interaction, target);
}

// Handle status check
async function handleLockdownStatus(interaction, lockdownSystem) {
    const guildId = interaction.guild.id;
    const lockdownData = lockdownSystem.getLockdownStatus(guildId);
    const emojis = global.emojiReplacer || {};

    if (!lockdownData) {
        return await interaction.reply({
            content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **No Active Lockdown**\n\nThe server is currently not under lockdown.`,
            ephemeral: true
        });
    }

    const safelist = lockdownSystem.getSafelist(guildId);
    const duration = Date.now() - lockdownData.activatedAt;
    const durationMinutes = Math.floor(duration / (1000 * 60));
    const durationHours = Math.floor(durationMinutes / 60);
    const remainingMinutes = durationMinutes % 60;

    let durationText;
    if (durationHours > 0) {
        durationText = `${durationHours}h ${remainingMinutes}m`;
    } else {
        durationText = `${durationMinutes}m`;
    }

    let statusText = `${emojis.getShield ? emojis.getShield() : '🚨'} **Lockdown Active**\n\n`;
    statusText += `**Reason:** ${lockdownData.reason}\n`;
    statusText += `**Activated by:** <@${lockdownData.activatedBy}>\n`;
    statusText += `**Duration:** ${durationText}\n`;
    statusText += `**Started:** <t:${Math.floor(lockdownData.activatedAt / 1000)}:F>\n\n`;

    if (safelist.roles.size > 0 || safelist.users.size > 0) {
        statusText += `**Allowed Access:**\n`;
        
        if (safelist.roles.size > 0) {
            const roleNames = Array.from(safelist.roles).map(roleId => {
                const role = interaction.guild.roles.cache.get(roleId);
                return role ? `<@&${roleId}>` : 'Unknown Role';
            }).join(', ');
            statusText += `• **Roles:** ${roleNames}\n`;
        }
        
        if (safelist.users.size > 0) {
            const userNames = Array.from(safelist.users).map(userId => `<@${userId}>`).join(', ');
            statusText += `• **Users:** ${userNames}\n`;
        }
    } else {
        statusText += `**Allowed Access:** None (Admin only)\n`;
    }

    await interaction.reply({
        content: statusText,
        ephemeral: true
    });
}
