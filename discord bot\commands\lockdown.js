const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const LockdownSystem = require('../utils/lockdownSystem');

// Initialize lockdown system
const lockdownSystem = new LockdownSystem();

module.exports = {
    data: new SlashCommandBuilder()
        .setName('lockdown')
        .setDescription('Advanced server lockdown system')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addSubcommand(subcommand =>
            subcommand
                .setName('on')
                .setDescription('Activate server lockdown')
                .addStringOption(option =>
                    option
                        .setName('reason')
                        .setDescription('Reason for the lockdown')
                        .setRequired(true)
                        .setMaxLength(500)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('off')
                .setDescription('Deactivate server lockdown')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('allow')
                .setDescription('Grant access to a user or role during lockdown')
                .addUserOption(option =>
                    option
                        .setName('user')
                        .setDescription('User to grant access to')
                        .setRequired(false)
                )
                .addRoleOption(option =>
                    option
                        .setName('role')
                        .setDescription('Role to grant access to')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('status')
                .setDescription('Check current lockdown status')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('emergency-unlock')
                .setDescription('Emergency unlock - force remove all lockdown permissions')
        ),

    async execute(interaction, args) {
        // Handle both slash commands and prefix commands
        const isSlashCommand = interaction.isCommand && interaction.isCommand();
        const isMessage = !isSlashCommand;

        let guild, member, user;

        if (isSlashCommand) {
            guild = interaction.guild;
            member = interaction.member;
            user = interaction.user;
        } else {
            // It's a message-based command
            guild = interaction.guild;
            member = interaction.member;
            user = interaction.author;
        }

        // Check permissions
        if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
            const errorMsg = '❌ You need Administrator permissions to use lockdown commands.';

            if (isSlashCommand) {
                return await interaction.reply({
                    content: errorMsg,
                    ephemeral: true
                });
            } else {
                return await interaction.reply(errorMsg);
            }
        }

        // Check bot permissions
        const botMember = guild.members.me;
        const requiredPermissions = [
            PermissionFlagsBits.ManageChannels,
            PermissionFlagsBits.ManageRoles,
            PermissionFlagsBits.ViewChannel,
            PermissionFlagsBits.SendMessages
        ];

        const missingPermissions = requiredPermissions.filter(perm => !botMember.permissions.has(perm));
        if (missingPermissions.length > 0) {
            const permNames = missingPermissions.map(perm =>
                Object.keys(PermissionFlagsBits).find(key => PermissionFlagsBits[key] === perm)
            ).join(', ');

            const errorMsg = `❌ Bot is missing required permissions: ${permNames}`;

            if (isSlashCommand) {
                return await interaction.reply({
                    content: errorMsg,
                    ephemeral: true
                });
            } else {
                return await interaction.reply(errorMsg);
            }
        }

        let subcommand;

        if (isSlashCommand) {
            subcommand = interaction.options.getSubcommand();
        } else {
            // For prefix commands, get subcommand from args
            subcommand = args[0]?.toLowerCase();
            if (!subcommand) {
                return await interaction.reply('❌ Please specify a subcommand: `on`, `off`, `allow`, `status`, or `emergency-unlock`');
            }
        }

        try {
            switch (subcommand) {
                case 'on':
                    await handleLockdownOn(interaction, lockdownSystem, args, isSlashCommand);
                    break;
                case 'off':
                    await handleLockdownOff(interaction, lockdownSystem, isSlashCommand);
                    break;
                case 'allow':
                    await handleLockdownAllow(interaction, lockdownSystem, args, isSlashCommand);
                    break;
                case 'status':
                    await handleLockdownStatus(interaction, lockdownSystem, isSlashCommand);
                    break;
                case 'emergency-unlock':
                    await handleEmergencyUnlock(interaction, lockdownSystem, isSlashCommand);
                    break;
                default:
                    const errorMsg = '❌ Unknown subcommand. Use: `on`, `off`, `allow`, `status`, or `emergency-unlock`';
                    if (isSlashCommand) {
                        await interaction.reply({
                            content: errorMsg,
                            ephemeral: true
                        });
                    } else {
                        await interaction.reply(errorMsg);
                    }
            }
        } catch (error) {
            console.error('Lockdown command error:', error);

            const errorMessage = '❌ An error occurred while executing the lockdown command.';

            if (isSlashCommand) {
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({ content: errorMessage, ephemeral: true });
                } else if (interaction.deferred) {
                    await interaction.editReply({ content: errorMessage });
                } else {
                    await interaction.followUp({ content: errorMessage, ephemeral: true });
                }
            } else {
                await interaction.reply(errorMessage);
            }
        }
    }
};

// Handle lockdown activation
async function handleLockdownOn(interaction, lockdownSystem, args, isSlashCommand) {
    let reason;

    if (isSlashCommand) {
        reason = interaction.options.getString('reason');
    } else {
        // For prefix commands, get reason from args (skip 'on' subcommand)
        reason = args.slice(1).join(' ');
        if (!reason) {
            return await interaction.reply('❌ Please provide a reason: `/lockdown on reason:Your reason here`');
        }
    }

    // Validate reason
    if (reason.length < 5) {
        const errorMsg = '❌ Lockdown reason must be at least 5 characters long.';
        if (isSlashCommand) {
            return await interaction.reply({
                content: errorMsg,
                ephemeral: true
            });
        } else {
            return await interaction.reply(errorMsg);
        }
    }

    await lockdownSystem.activateLockdown(interaction, reason);
}

// Handle lockdown deactivation
async function handleLockdownOff(interaction, lockdownSystem, isSlashCommand) {
    await lockdownSystem.deactivateLockdown(interaction);
}

// Handle granting access
async function handleLockdownAllow(interaction, lockdownSystem, args, isSlashCommand) {
    let user, role;

    if (isSlashCommand) {
        user = interaction.options.getMember('user');
        role = interaction.options.getRole('role');
    } else {
        // For prefix commands, this is more complex - we'll need to parse mentions
        const errorMsg = '❌ For prefix commands, please use slash command: `/lockdown allow user:@user` or `/lockdown allow role:@role`';
        return await interaction.reply(errorMsg);
    }

    // Check if either user or role is provided
    if (!user && !role) {
        const errorMsg = '❌ You must specify either a user or a role to grant access to.';
        if (isSlashCommand) {
            return await interaction.reply({
                content: errorMsg,
                ephemeral: true
            });
        } else {
            return await interaction.reply(errorMsg);
        }
    }

    // Check if both are provided
    if (user && role) {
        const errorMsg = '❌ You can only specify either a user OR a role, not both.';
        if (isSlashCommand) {
            return await interaction.reply({
                content: errorMsg,
                ephemeral: true
            });
        } else {
            return await interaction.reply(errorMsg);
        }
    }

    const target = user || role;
    
    // Additional validation for roles
    if (role) {
        // Don't allow @everyone
        if (role.id === interaction.guild.roles.everyone.id) {
            const errorMsg = '❌ Cannot grant access to @everyone role.';
            if (isSlashCommand) {
                return await interaction.reply({
                    content: errorMsg,
                    ephemeral: true
                });
            } else {
                return await interaction.reply(errorMsg);
            }
        }

        // Don't allow bot roles
        if (role.managed) {
            const errorMsg = '❌ Cannot grant access to managed/bot roles.';
            if (isSlashCommand) {
                return await interaction.reply({
                    content: errorMsg,
                    ephemeral: true
                });
            } else {
                return await interaction.reply(errorMsg);
            }
        }
    }

    await lockdownSystem.allowAccess(interaction, target);
}

// Handle status check
async function handleLockdownStatus(interaction, lockdownSystem, isSlashCommand) {
    const guildId = interaction.guild.id;
    const lockdownData = lockdownSystem.getLockdownStatus(guildId);
    const emojis = global.emojiReplacer || {};

    if (!lockdownData) {
        const statusMsg = `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **No Active Lockdown**\n\nThe server is currently not under lockdown.`;
        if (isSlashCommand) {
            return await interaction.reply({
                content: statusMsg,
                ephemeral: true
            });
        } else {
            return await interaction.reply(statusMsg);
        }
    }

    const safelist = lockdownSystem.getSafelist(guildId);
    const duration = Date.now() - lockdownData.activatedAt;
    const durationMinutes = Math.floor(duration / (1000 * 60));
    const durationHours = Math.floor(durationMinutes / 60);
    const remainingMinutes = durationMinutes % 60;

    let durationText;
    if (durationHours > 0) {
        durationText = `${durationHours}h ${remainingMinutes}m`;
    } else {
        durationText = `${durationMinutes}m`;
    }

    let statusText = `${emojis.getShield ? emojis.getShield() : '🚨'} **Lockdown Active**\n\n`;
    statusText += `**Reason:** ${lockdownData.reason}\n`;
    statusText += `**Activated by:** <@${lockdownData.activatedBy}>\n`;
    statusText += `**Duration:** ${durationText}\n`;
    statusText += `**Started:** <t:${Math.floor(lockdownData.activatedAt / 1000)}:F>\n\n`;

    if (safelist.roles.size > 0 || safelist.users.size > 0) {
        statusText += `**Allowed Access:**\n`;
        
        if (safelist.roles.size > 0) {
            const roleNames = Array.from(safelist.roles).map(roleId => {
                const role = interaction.guild.roles.cache.get(roleId);
                return role ? `<@&${roleId}>` : 'Unknown Role';
            }).join(', ');
            statusText += `• **Roles:** ${roleNames}\n`;
        }
        
        if (safelist.users.size > 0) {
            const userNames = Array.from(safelist.users).map(userId => `<@${userId}>`).join(', ');
            statusText += `• **Users:** ${userNames}\n`;
        }
    } else {
        statusText += `**Allowed Access:** None (Admin only)\n`;
    }

    if (isSlashCommand) {
        await interaction.reply({
            content: statusText,
            ephemeral: true
        });
    } else {
        await interaction.reply(statusText);
    }
}

// Handle emergency unlock
async function handleEmergencyUnlock(interaction, lockdownSystem, isSlashCommand) {
    // Confirm with user first
    const confirmMsg = '⚠️ **EMERGENCY UNLOCK WARNING**\n\nThis will forcefully remove ALL lockdown permissions from ALL channels and delete lockdown data.\n\n**This action cannot be undone!**\n\nType `CONFIRM` to proceed:';

    if (isSlashCommand) {
        await interaction.reply({
            content: confirmMsg,
            ephemeral: true
        });
    } else {
        await interaction.reply(confirmMsg);
    }

    // Wait for confirmation (simplified for now - just proceed)
    await lockdownSystem.emergencyUnlock(interaction);
}
