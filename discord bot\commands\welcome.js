const { SlashCommandBuilder, PermissionFlagsBits, ChannelType } = require('discord.js');
const Database = require('../database/database');

module.exports = {
    data: {
        name: 'welcome',
        description: 'Welcome and goodbye system configuration'
    },
    slashData: new SlashCommandBuilder()
        .setName('welcome')
        .setDescription('Welcome and goodbye system configuration')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
        .setDMPermission(false)
        .addSubcommand(subcommand =>
            subcommand
                .setName('setup')
                .setDescription('Setup welcome/goodbye system')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Channel for welcome/goodbye messages')
                        .addChannelTypes(ChannelType.GuildText)
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('message')
                .setDescription('Configure welcome/goodbye messages')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('Message type to configure')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Welcome Message', value: 'welcome' },
                            { name: 'Goodbye Message', value: 'goodbye' }
                        ))
                .addStringOption(option =>
                    option.setName('message')
                        .setDescription('Custom message (use {user} for mention, {username} for name, {server} for server name)')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('autorole')
                .setDescription('Configure auto-role for new members')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role to give new members (leave empty to disable)')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('enable')
                .setDescription('Enable welcome/goodbye features')
                .addStringOption(option =>
                    option.setName('feature')
                        .setDescription('Feature to enable')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Welcome Messages', value: 'welcome' },
                            { name: 'Goodbye Messages', value: 'goodbye' },
                            { name: 'Auto-Role', value: 'autorole' },
                            { name: 'All Features', value: 'all' }
                        )))
        .addSubcommand(subcommand =>
            subcommand
                .setName('disable')
                .setDescription('Disable welcome/goodbye features')
                .addStringOption(option =>
                    option.setName('feature')
                        .setDescription('Feature to disable')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Welcome Messages', value: 'welcome' },
                            { name: 'Goodbye Messages', value: 'goodbye' },
                            { name: 'Auto-Role', value: 'autorole' },
                            { name: 'All Features', value: 'all' }
                        )))
        .addSubcommand(subcommand =>
            subcommand
                .setName('test')
                .setDescription('Test welcome/goodbye messages')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('Type of message to test')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Welcome Message', value: 'welcome' },
                            { name: 'Goodbye Message', value: 'goodbye' }
                        )))
        .addSubcommand(subcommand =>
            subcommand
                .setName('status')
                .setDescription('View current welcome/goodbye configuration')),

    async execute(interaction) {
        // Check permissions
        if (!interaction.member.permissions.has(PermissionFlagsBits.ManageGuild)) {
            const errorEmoji = global.emojiReplacer ? global.emojiReplacer.getError() : '❌';
            return await interaction.reply({
                content: `${errorEmoji} You need **Manage Server** permission to use this command.`,
                flags: 64
            });
        }

        // Defer reply immediately
        await interaction.deferReply({ flags: 64 });

        const subcommand = interaction.options.getSubcommand();
        const emojis = global.emojiReplacer || {};

        try {
            switch (subcommand) {
                case 'setup':
                    await this.handleSetup(interaction, emojis);
                    break;
                case 'message':
                    await this.handleMessage(interaction, emojis);
                    break;
                case 'autorole':
                    await this.handleAutorole(interaction, emojis);
                    break;
                case 'enable':
                    await this.handleEnable(interaction, emojis);
                    break;
                case 'disable':
                    await this.handleDisable(interaction, emojis);
                    break;
                case 'test':
                    await this.handleTest(interaction, emojis);
                    break;
                case 'status':
                    await this.handleStatus(interaction, emojis);
                    break;
                default:
                    await interaction.editReply({
                        content: `${emojis.getError ? emojis.getError() : '❌'} Unknown subcommand.`
                    });
            }
        } catch (error) {
            console.error('Welcome command error:', error);
            
            if (!interaction.replied) {
                await interaction.editReply({
                    content: `${emojis.getError ? emojis.getError() : '❌'} An error occurred while executing the command.`
                });
            }
        }
    },

    async handleSetup(interaction, emojis) {
        const channel = interaction.options.getChannel('channel');

        try {
            const settings = {
                guild_id: interaction.guild.id,
                welcome_channel: channel.id,
                welcome_enabled: true,
                goodbye_enabled: true,
                welcome_message: 'Welcome {user} to **{server}**! 🎉',
                goodbye_message: 'Goodbye **{username}**, thanks for being part of **{server}**! 👋'
            };

            await Database.setWelcomeSettings(settings);

            const successEmbed = global.embedBuilder.success(
                'Welcome System Setup Complete!',
                `Welcome/goodbye system has been successfully configured in ${channel}`,
                {
                    fields: [
                        { name: `${global.emojiReplacer.getWelcome()} Welcome Messages`, value: `${global.emojiReplacer.getSuccess()} Enabled`, inline: true },
                        { name: `${global.emojiReplacer.getWelcome()} Goodbye Messages`, value: `${global.emojiReplacer.getSuccess()} Enabled`, inline: true },
                        { name: `${global.emojiReplacer.getSettings()} Default Messages`, value: 'Default welcome and goodbye messages have been set', inline: false },
                        { name: `${global.emojiReplacer.getInfo()} Next Steps`, value: 'Use `/welcome message` to customize messages\nUse `/welcome autorole` to set auto-role\nUse `/welcome test` to preview messages', inline: false }
                    ]
                }
            );

            await interaction.editReply({ embeds: [successEmbed] });

        } catch (error) {
            console.error('Failed to setup welcome system:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to setup welcome system.`
            });
        }
    },

    async handleMessage(interaction, emojis) {
        const type = interaction.options.getString('type');
        const message = interaction.options.getString('message');

        try {
            const settings = await Database.getWelcomeSettings(interaction.guild.id) || {};
            settings.guild_id = interaction.guild.id;
            
            if (type === 'welcome') {
                settings.welcome_message = message;
            } else {
                settings.goodbye_message = message;
            }

            await Database.setWelcomeSettings(settings);

            const messageType = type === 'welcome' ? 'Welcome' : 'Goodbye';
            
            await interaction.editReply({
                content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **${messageType} message** has been updated!\n\n**Preview:** ${this.formatMessage(message, interaction.user, interaction.guild)}`
            });

        } catch (error) {
            console.error('Failed to update message:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to update message.`
            });
        }
    },

    async handleAutorole(interaction, emojis) {
        const role = interaction.options.getRole('role');

        try {
            const settings = await Database.getWelcomeSettings(interaction.guild.id) || {};
            settings.guild_id = interaction.guild.id;
            
            if (role) {
                settings.autorole_id = role.id;
                settings.autorole_enabled = true;
            } else {
                settings.autorole_enabled = false;
                settings.autorole_id = null;
            }

            await Database.setWelcomeSettings(settings);

            if (role) {
                await interaction.editReply({
                    content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Auto-role** has been set to ${role}. New members will automatically receive this role.`
                });
            } else {
                await interaction.editReply({
                    content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Auto-role** has been disabled.`
                });
            }

        } catch (error) {
            console.error('Failed to configure auto-role:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to configure auto-role.`
            });
        }
    },

    async handleEnable(interaction, emojis) {
        const feature = interaction.options.getString('feature');

        try {
            const settings = await Database.getWelcomeSettings(interaction.guild.id) || {};
            settings.guild_id = interaction.guild.id;

            if (feature === 'all') {
                settings.welcome_enabled = true;
                settings.goodbye_enabled = true;
                settings.autorole_enabled = true;
            } else if (feature === 'welcome') {
                settings.welcome_enabled = true;
            } else if (feature === 'goodbye') {
                settings.goodbye_enabled = true;
            } else if (feature === 'autorole') {
                settings.autorole_enabled = true;
            }

            await Database.setWelcomeSettings(settings);

            const featureNames = {
                welcome: 'Welcome Messages',
                goodbye: 'Goodbye Messages',
                autorole: 'Auto-Role',
                all: 'All Features'
            };

            await interaction.editReply({
                content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **${featureNames[feature]}** have been enabled.`
            });

        } catch (error) {
            console.error('Failed to enable feature:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to enable feature.`
            });
        }
    },

    async handleDisable(interaction, emojis) {
        const feature = interaction.options.getString('feature');

        try {
            const settings = await Database.getWelcomeSettings(interaction.guild.id) || {};
            settings.guild_id = interaction.guild.id;

            if (feature === 'all') {
                settings.welcome_enabled = false;
                settings.goodbye_enabled = false;
                settings.autorole_enabled = false;
            } else if (feature === 'welcome') {
                settings.welcome_enabled = false;
            } else if (feature === 'goodbye') {
                settings.goodbye_enabled = false;
            } else if (feature === 'autorole') {
                settings.autorole_enabled = false;
            }

            await Database.setWelcomeSettings(settings);

            const featureNames = {
                welcome: 'Welcome Messages',
                goodbye: 'Goodbye Messages',
                autorole: 'Auto-Role',
                all: 'All Features'
            };

            await interaction.editReply({
                content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **${featureNames[feature]}** have been disabled.`
            });

        } catch (error) {
            console.error('Failed to disable feature:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to disable feature.`
            });
        }
    },

    async handleTest(interaction, emojis) {
        const type = interaction.options.getString('type');

        try {
            const settings = await Database.getWelcomeSettings(interaction.guild.id);

            if (!settings) {
                return await interaction.editReply({
                    content: `${emojis.getError ? emojis.getError() : '❌'} Welcome system not configured. Use \`/welcome setup\` first.`
                });
            }

            const message = type === 'welcome' ? settings.welcome_message : settings.goodbye_message;
            const formattedMessage = this.formatMessage(message, interaction.user, interaction.guild);

            const embed = new EmbedBuilder()
                .setColor(type === 'welcome' ? 0x00FF00 : 0xFF6B35)
                .setTitle(`${type === 'welcome' ? '👋' : '👋'} ${type === 'welcome' ? 'Welcome' : 'Goodbye'} Message Test`)
                .setDescription(formattedMessage)
                .setThumbnail(interaction.user.displayAvatarURL())
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Failed to test message:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to test message.`
            });
        }
    },

    async handleStatus(interaction, emojis) {
        try {
            const settings = await Database.getWelcomeSettings(interaction.guild.id);

            if (!settings) {
                return await interaction.editReply({
                    content: `${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} No welcome system configuration found. Use \`/welcome setup\` to configure.`
                });
            }

            const embed = new EmbedBuilder()
                .setColor(0x0099FF)
                .setTitle(`${emojis.getList ? emojis.getList() : '📋'} Welcome System Configuration`)
                .setDescription('Current welcome/goodbye settings for this server:')
                .setTimestamp();

            const getChannelMention = (channelId) => {
                if (!channelId) return 'Not set';
                const channel = interaction.guild.channels.cache.get(channelId);
                return channel ? channel.toString() : 'Channel not found';
            };

            const getRoleMention = (roleId) => {
                if (!roleId) return 'Not set';
                const role = interaction.guild.roles.cache.get(roleId);
                return role ? role.toString() : 'Role not found';
            };

            const getStatus = (enabled) => enabled ? '🟢 Enabled' : '🔴 Disabled';

            embed.addFields(
                {
                    name: '📍 Welcome Channel',
                    value: getChannelMention(settings.welcome_channel),
                    inline: false
                },
                {
                    name: '👋 Welcome Messages',
                    value: `**Status:** ${getStatus(settings.welcome_enabled)}\n**Message:** ${settings.welcome_message || 'Not set'}`,
                    inline: false
                },
                {
                    name: '👋 Goodbye Messages',
                    value: `**Status:** ${getStatus(settings.goodbye_enabled)}\n**Message:** ${settings.goodbye_message || 'Not set'}`,
                    inline: false
                },
                {
                    name: '🎭 Auto-Role',
                    value: `**Status:** ${getStatus(settings.autorole_enabled)}\n**Role:** ${getRoleMention(settings.autorole_id)}`,
                    inline: false
                }
            );

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Failed to get welcome status:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to get welcome status.`
            });
        }
    },

    formatMessage(message, user, guild) {
        if (!message) return 'No message set';
        
        return message
            .replace(/{user}/g, user.toString())
            .replace(/{username}/g, user.username)
            .replace(/{server}/g, guild.name)
            .replace(/{membercount}/g, guild.memberCount.toString());
    }
};
