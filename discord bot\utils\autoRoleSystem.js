const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const Database = require('../database/database');

class AutoRoleSystem {
    constructor(client) {
        this.client = client;
        this.autoRoles = new Map(); // guildId -> { roles: Set(roleIds), enabled: boolean }
        this.setupDatabase();
        this.loadAutoRoles();
    }

    // Setup database tables
    setupDatabase() {
        try {
            // Database tables will be created when database is properly connected
            console.log('💾 Auto Roles system ready (using existing database setup)');

        } catch (error) {
            console.error('Error setting up auto roles database:', error);
        }
    }

    // Load auto roles from database
    loadAutoRoles() {
        try {
            // For now, start with empty auto roles
            // Will be loaded when database is properly connected
            console.log('🤖 Auto Roles system initialized (will load data when database is ready)');

        } catch (error) {
            console.error('Error loading auto roles:', error);
        }
    }

    // Add auto role
    async addAutoRole(guildId, roleId) {
        try {
            // Store in database if available
            if (global.database && global.database.db) {
                global.database.db.run(`
                    INSERT OR REPLACE INTO auto_roles (guild_id, role_id, enabled)
                    VALUES (?, ?, 1)
                `, [guildId, roleId]);
            }

            // Add to memory
            if (!this.autoRoles.has(guildId)) {
                this.autoRoles.set(guildId, {
                    roles: new Set(),
                    enabled: true,
                    delay: 0,
                    welcomeMessage: null,
                    dmNewMembers: false
                });
            }
            
            this.autoRoles.get(guildId).roles.add(roleId);
            
            console.log(`🤖 AUTO ROLE [${guildId}] Added role ${roleId} to auto-assign list`);
            return true;
        } catch (error) {
            console.error('Error adding auto role:', error);
            return false;
        }
    }

    // Remove auto role
    async removeAutoRole(guildId, roleId) {
        try {
            Database.prepare(`
                DELETE FROM auto_roles WHERE guild_id = ? AND role_id = ?
            `).run(guildId, roleId);

            // Remove from memory
            if (this.autoRoles.has(guildId)) {
                this.autoRoles.get(guildId).roles.delete(roleId);
            }
            
            console.log(`🗑️ AUTO ROLE [${guildId}] Removed role ${roleId} from auto-assign list`);
            return true;
        } catch (error) {
            console.error('Error removing auto role:', error);
            return false;
        }
    }

    // Set auto role system settings
    async setSettings(guildId, settings) {
        try {
            const { enabled, delay, welcomeMessage, dmNewMembers } = settings;
            
            Database.prepare(`
                INSERT OR REPLACE INTO auto_role_settings 
                (guild_id, enabled, delay_seconds, welcome_message, dm_new_members)
                VALUES (?, ?, ?, ?, ?)
            `).run(guildId, enabled ? 1 : 0, delay || 0, welcomeMessage || null, dmNewMembers ? 1 : 0);

            // Update memory
            if (!this.autoRoles.has(guildId)) {
                this.autoRoles.set(guildId, {
                    roles: new Set(),
                    enabled: enabled,
                    delay: delay || 0,
                    welcomeMessage: welcomeMessage,
                    dmNewMembers: dmNewMembers || false
                });
            } else {
                const autoRoleData = this.autoRoles.get(guildId);
                autoRoleData.enabled = enabled;
                autoRoleData.delay = delay || 0;
                autoRoleData.welcomeMessage = welcomeMessage;
                autoRoleData.dmNewMembers = dmNewMembers || false;
            }
            
            console.log(`⚙️ AUTO ROLE [${guildId}] Updated settings`);
            return true;
        } catch (error) {
            console.error('Error setting auto role settings:', error);
            return false;
        }
    }

    // Handle new member join
    async handleMemberJoin(member) {
        try {
            const guildId = member.guild.id;
            const autoRoleData = this.autoRoles.get(guildId);
            
            if (!autoRoleData || !autoRoleData.enabled || autoRoleData.roles.size === 0) {
                return;
            }

            // Apply delay if configured
            if (autoRoleData.delay > 0) {
                setTimeout(async () => {
                    await this.assignRolesToMember(member, autoRoleData);
                }, autoRoleData.delay * 1000);
            } else {
                await this.assignRolesToMember(member, autoRoleData);
            }

        } catch (error) {
            console.error('Error handling member join for auto roles:', error);
        }
    }

    // Assign roles to member
    async assignRolesToMember(member, autoRoleData) {
        try {
            const guild = member.guild;
            const rolesToAdd = [];
            
            // Validate roles and collect valid ones
            for (const roleId of autoRoleData.roles) {
                const role = guild.roles.cache.get(roleId);
                if (role && !member.roles.cache.has(roleId)) {
                    // Check if bot can assign this role
                    const botMember = guild.members.me;
                    if (botMember.roles.highest.position > role.position) {
                        rolesToAdd.push(role);
                    } else {
                        console.warn(`🤖 AUTO ROLE [${guild.id}] Cannot assign role ${role.name} - insufficient permissions`);
                    }
                }
            }

            // Assign roles
            if (rolesToAdd.length > 0) {
                await member.roles.add(rolesToAdd, 'Auto role assignment');
                
                const roleNames = rolesToAdd.map(r => r.name).join(', ');
                console.log(`🤖 AUTO ROLE [${guild.name}] Assigned roles to ${member.user.tag}: ${roleNames}`);

                // Send welcome DM if enabled
                if (autoRoleData.dmNewMembers && autoRoleData.welcomeMessage) {
                    try {
                        const emojis = global.emojiReplacer || {};
                        const welcomeMsg = autoRoleData.welcomeMessage
                            .replace('{user}', member.user.username)
                            .replace('{server}', guild.name)
                            .replace('{roles}', roleNames);

                        await member.send({
                            content: `${emojis.getWelcome ? emojis.getWelcome() : '👋'} **Welcome to ${guild.name}!**\n\n${welcomeMsg}`
                        });
                    } catch (error) {
                        // DM failed, ignore
                    }
                }
            }

        } catch (error) {
            console.error('Error assigning auto roles to member:', error);
        }
    }

    // Get auto role settings for guild
    getGuildSettings(guildId) {
        const autoRoleData = this.autoRoles.get(guildId);
        
        if (!autoRoleData) {
            return {
                enabled: false,
                roles: [],
                delay: 0,
                welcomeMessage: null,
                dmNewMembers: false
            };
        }

        return {
            enabled: autoRoleData.enabled,
            roles: Array.from(autoRoleData.roles),
            delay: autoRoleData.delay,
            welcomeMessage: autoRoleData.welcomeMessage,
            dmNewMembers: autoRoleData.dmNewMembers
        };
    }

    // Get auto roles for guild
    getGuildAutoRoles(guildId) {
        const autoRoleData = this.autoRoles.get(guildId);
        return autoRoleData ? Array.from(autoRoleData.roles) : [];
    }

    // Check if auto roles are enabled for guild
    isEnabled(guildId) {
        const autoRoleData = this.autoRoles.get(guildId);
        return autoRoleData ? autoRoleData.enabled : false;
    }

    // Toggle auto role system for guild
    async toggleSystem(guildId, enabled) {
        try {
            Database.prepare(`
                INSERT OR REPLACE INTO auto_role_settings 
                (guild_id, enabled)
                VALUES (?, ?)
            `).run(guildId, enabled ? 1 : 0);

            // Update memory
            if (!this.autoRoles.has(guildId)) {
                this.autoRoles.set(guildId, {
                    roles: new Set(),
                    enabled: enabled,
                    delay: 0,
                    welcomeMessage: null,
                    dmNewMembers: false
                });
            } else {
                this.autoRoles.get(guildId).enabled = enabled;
            }
            
            console.log(`🤖 AUTO ROLE [${guildId}] System ${enabled ? 'enabled' : 'disabled'}`);
            return true;
        } catch (error) {
            console.error('Error toggling auto role system:', error);
            return false;
        }
    }
}

module.exports = AutoRoleSystem;
