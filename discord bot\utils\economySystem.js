const { EmbedBuilder } = require('discord.js');
const Database = require('better-sqlite3');

class EconomySystem {
    constructor() {
        this.db = null;
        this.coinRates = {
            message: 5,        // 5 coins per message
            voiceMinute: 10,   // 10 coins per minute in voice
            dailyBonus: 100,   // 100 coins daily bonus (automatic)
            weeklyBonus: 500,  // 500 coins weekly bonus (automatic)
            rankBonus: 50      // 50 coins for ranking up (automatic)
        };
        
        // Voice tracking
        this.voiceUsers = new Map(); // userId -> { joinTime, channelId }
        this.voiceUpdateInterval = null;
        
        // Message cooldowns (prevent spam farming)
        this.messageCooldowns = new Map(); // userId -> lastMessageTime
        this.messageCooldownTime = 60000; // 1 minute cooldown between coin rewards
    }

    async initialize(client) {
        this.client = client;
        this.db = new Database('./data/economy.db');
        
        // Create tables
        this.createTables();
        
        // Start voice tracking
        this.startVoiceTracking();

        // Start automatic bonus systems
        this.startAutomaticBonuses();

        console.log('💰 Economy System initialized!');
    }

    createTables() {
        // Users table for economy data
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS economy_users (
                user_id TEXT PRIMARY KEY,
                guild_id TEXT NOT NULL,
                coins INTEGER DEFAULT 0,
                total_earned INTEGER DEFAULT 0,
                messages_sent INTEGER DEFAULT 0,
                voice_minutes INTEGER DEFAULT 0,
                rank_level INTEGER DEFAULT 1,
                last_daily DATETIME DEFAULT NULL,
                last_weekly DATETIME DEFAULT NULL,
                last_rank_check DATETIME DEFAULT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Add missing columns to existing table if they don't exist
        try {
            this.db.exec(`ALTER TABLE economy_users ADD COLUMN rank_level INTEGER DEFAULT 1`);
        } catch (error) {
            // Column already exists, ignore error
        }

        try {
            this.db.exec(`ALTER TABLE economy_users ADD COLUMN last_daily DATETIME DEFAULT NULL`);
        } catch (error) {
            // Column already exists, ignore error
        }

        try {
            this.db.exec(`ALTER TABLE economy_users ADD COLUMN last_weekly DATETIME DEFAULT NULL`);
        } catch (error) {
            // Column already exists, ignore error
        }

        try {
            this.db.exec(`ALTER TABLE economy_users ADD COLUMN last_rank_check DATETIME DEFAULT NULL`);
        } catch (error) {
            // Column already exists, ignore error
        }

        try {
            this.db.exec(`ALTER TABLE economy_users ADD COLUMN last_work DATETIME DEFAULT NULL`);
        } catch (error) {
            // Column already exists, ignore error
        }

        // Transactions table for tracking coin history
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS economy_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                guild_id TEXT NOT NULL,
                amount INTEGER NOT NULL,
                type TEXT NOT NULL,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        console.log('💾 Economy database tables created');
    }

    // Get or create user economy data
    getUser(userId, guildId) {
        let user = this.db.prepare('SELECT * FROM economy_users WHERE user_id = ? AND guild_id = ?').get(userId, guildId);
        
        if (!user) {
            try {
                this.db.prepare(`
                    INSERT OR IGNORE INTO economy_users (user_id, guild_id, coins, total_earned, messages_sent, voice_minutes)
                    VALUES (?, ?, 0, 0, 0, 0)
                `).run(userId, guildId);

                user = this.db.prepare('SELECT * FROM economy_users WHERE user_id = ? AND guild_id = ?').get(userId, guildId);
            } catch (error) {
                console.error('Economy system error in getUser:', error);
                // Try to get user again in case it was created by another process
                user = this.db.prepare('SELECT * FROM economy_users WHERE user_id = ? AND guild_id = ?').get(userId, guildId);
                if (!user) {
                    // Create default user object if still not found
                    user = {
                        user_id: userId,
                        guild_id: guildId,
                        coins: 0,
                        total_earned: 0,
                        messages_sent: 0,
                        voice_minutes: 0,
                        rank_level: 1
                    };
                }
            }
        }
        
        return user;
    }

    // Add coins to user
    addCoins(userId, guildId, amount, type, description = '') {
        const user = this.getUser(userId, guildId);
        
        // Update user coins
        this.db.prepare(`
            UPDATE economy_users 
            SET coins = coins + ?, total_earned = total_earned + ?, updated_at = CURRENT_TIMESTAMP
            WHERE user_id = ? AND guild_id = ?
        `).run(amount, amount, userId, guildId);

        // Log transaction
        this.db.prepare(`
            INSERT INTO economy_transactions (user_id, guild_id, amount, type, description)
            VALUES (?, ?, ?, ?, ?)
        `).run(userId, guildId, amount, type, description);

        console.log(`💰 ECONOMY [${guildId}] [${userId}] +${amount} coins (${type})`);
        return amount;
    }

    // Remove coins from user
    removeCoins(userId, guildId, amount, type, description = '') {
        const user = this.getUser(userId, guildId);
        
        if (user.coins < amount) {
            return false; // Insufficient funds
        }

        // Update user coins
        this.db.prepare(`
            UPDATE economy_users 
            SET coins = coins - ?, updated_at = CURRENT_TIMESTAMP
            WHERE user_id = ? AND guild_id = ?
        `).run(amount, userId, guildId);

        // Log transaction
        this.db.prepare(`
            INSERT INTO economy_transactions (user_id, guild_id, amount, type, description)
            VALUES (?, ?, ?, ?, ?)
        `).run(userId, guildId, -amount, type, description);

        console.log(`💰 ECONOMY [${guildId}] [${userId}] -${amount} coins (${type})`);
        return true;
    }

    // Handle message coins
    async handleMessage(message) {
        if (message.author.bot) return;
        
        const userId = message.author.id;
        const guildId = message.guild.id;
        const now = Date.now();
        
        // Check cooldown
        const lastMessage = this.messageCooldowns.get(userId);
        if (lastMessage && (now - lastMessage) < this.messageCooldownTime) {
            return; // Still in cooldown
        }

        // Update cooldown
        this.messageCooldowns.set(userId, now);

        // Add coins for message
        const coinsEarned = this.addCoins(userId, guildId, this.coinRates.message, 'message', 'Message sent');

        // Update message count
        this.db.prepare(`
            UPDATE economy_users
            SET messages_sent = messages_sent + 1
            WHERE user_id = ? AND guild_id = ?
        `).run(userId, guildId);

        // Check for automatic bonuses and rank ups
        this.checkAutomaticBonuses(userId, guildId);
        this.checkRankUp(userId, guildId);

        return coinsEarned;
    }

    // Handle voice state updates
    async handleVoiceStateUpdate(oldState, newState) {
        const userId = newState.member.id;
        const guildId = newState.guild.id;

        // User joined voice channel
        if (!oldState.channelId && newState.channelId) {
            this.voiceUsers.set(userId, {
                joinTime: Date.now(),
                channelId: newState.channelId,
                guildId: guildId
            });
            console.log(`🎤 VOICE [${guildId}] [${userId}] Joined voice channel`);
        }
        
        // User left voice channel
        else if (oldState.channelId && !newState.channelId) {
            const voiceData = this.voiceUsers.get(userId);
            if (voiceData) {
                const timeSpent = Date.now() - voiceData.joinTime;
                const minutesSpent = Math.floor(timeSpent / 60000); // Convert to minutes
                
                if (minutesSpent > 0) {
                    const coinsEarned = minutesSpent * this.coinRates.voiceMinute;
                    this.addCoins(userId, guildId, coinsEarned, 'voice', `${minutesSpent} minutes in voice`);
                    
                    // Update voice minutes
                    this.db.prepare(`
                        UPDATE economy_users 
                        SET voice_minutes = voice_minutes + ?
                        WHERE user_id = ? AND guild_id = ?
                    `).run(minutesSpent, userId, guildId);
                }
                
                this.voiceUsers.delete(userId);
                console.log(`🎤 VOICE [${guildId}] [${userId}] Left voice channel (${minutesSpent} minutes)`);
            }
        }
    }

    // Start voice tracking interval
    startVoiceTracking() {
        // Award coins every minute for users in voice
        this.voiceUpdateInterval = setInterval(() => {
            for (const [userId, voiceData] of this.voiceUsers.entries()) {
                const timeSpent = Date.now() - voiceData.joinTime;
                const minutesSpent = Math.floor(timeSpent / 60000);
                
                // Award coins for every minute (minimum 1 minute)
                if (minutesSpent > 0) {
                    const coinsEarned = this.coinRates.voiceMinute;
                    this.addCoins(userId, voiceData.guildId, coinsEarned, 'voice', '1 minute in voice');
                    
                    // Update voice minutes
                    this.db.prepare(`
                        UPDATE economy_users 
                        SET voice_minutes = voice_minutes + 1
                        WHERE user_id = ? AND guild_id = ?
                    `).run(userId, voiceData.guildId);
                    
                    // Reset join time for next minute
                    voiceData.joinTime = Date.now();
                }
            }
        }, 60000); // Every minute

        console.log('🎤 Voice tracking started - coins awarded every minute');
    }

    // Get user balance
    getBalance(userId, guildId) {
        const user = this.getUser(userId, guildId);
        return user.coins;
    }

    // Get user stats
    getUserStats(userId, guildId) {
        const user = this.getUser(userId, guildId);
        return {
            coins: user.coins,
            totalEarned: user.total_earned,
            messagesSent: user.messages_sent,
            voiceMinutes: user.voice_minutes,
            rankLevel: user.rank_level || 1
        };
    }

    // Calculate rank based on total earned coins
    calculateRank(totalEarned) {
        if (totalEarned >= 10000) return 10; // Master
        if (totalEarned >= 7500) return 9;   // Expert
        if (totalEarned >= 5000) return 8;   // Advanced
        if (totalEarned >= 3500) return 7;   // Skilled
        if (totalEarned >= 2500) return 6;   // Experienced
        if (totalEarned >= 1500) return 5;   // Intermediate
        if (totalEarned >= 1000) return 4;   // Regular
        if (totalEarned >= 500) return 3;    // Active
        if (totalEarned >= 200) return 2;    // Beginner
        return 1; // Newcomer
    }

    // Get rank name
    getRankName(level) {
        const ranks = {
            1: 'Newcomer',
            2: 'Beginner',
            3: 'Active',
            4: 'Regular',
            5: 'Intermediate',
            6: 'Experienced',
            7: 'Skilled',
            8: 'Advanced',
            9: 'Expert',
            10: 'Master'
        };
        return ranks[level] || 'Unknown';
    }

    // Calculate rank requirement
    calculateRankRequirement(level) {
        const requirements = {
            1: 0,
            2: 200,
            3: 500,
            4: 1000,
            5: 1500,
            6: 2500,
            7: 3500,
            8: 5000,
            9: 7500,
            10: 10000
        };
        return requirements[level] || null;
    }

    // Check for rank up (automatic)
    checkRankUp(userId, guildId) {
        const user = this.getUser(userId, guildId);
        const currentRank = user.rank_level || 1;
        const newRank = this.calculateRank(user.total_earned);

        if (newRank > currentRank) {
            // User ranked up!
            this.db.prepare(`
                UPDATE economy_users
                SET rank_level = ?, last_rank_check = CURRENT_TIMESTAMP
                WHERE user_id = ? AND guild_id = ?
            `).run(newRank, userId, guildId);

            // Award rank up bonus
            const bonus = this.coinRates.rankBonus * (newRank - currentRank);
            this.addCoins(userId, guildId, bonus, 'rank_bonus', `Ranked up to ${this.getRankName(newRank)}`);

            console.log(`🏆 RANK UP [${guildId}] [${userId}] ${this.getRankName(currentRank)} → ${this.getRankName(newRank)} (+${bonus} coins)`);
            return { ranked: true, oldRank: currentRank, newRank, bonus };
        }

        return { ranked: false };
    }

    // Check for automatic daily/weekly bonuses
    checkAutomaticBonuses(userId, guildId) {
        try {
            const user = this.getUser(userId, guildId);
            if (!user) return;

            const now = new Date();

            // Check daily bonus
            const lastDaily = user.last_daily ? new Date(user.last_daily) : null;
        if (!lastDaily || (now - lastDaily) >= 24 * 60 * 60 * 1000) {
            // Award daily bonus
            this.addCoins(userId, guildId, this.coinRates.dailyBonus, 'daily_auto', 'Automatic daily bonus');

            this.db.prepare(`
                UPDATE economy_users
                SET last_daily = CURRENT_TIMESTAMP
                WHERE user_id = ? AND guild_id = ?
            `).run(userId, guildId);

            console.log(`🎁 DAILY BONUS [${guildId}] [${userId}] +${this.coinRates.dailyBonus} coins (automatic)`);
        }

        // Check weekly bonus
        const lastWeekly = user.last_weekly ? new Date(user.last_weekly) : null;
        if (!lastWeekly || (now - lastWeekly) >= 7 * 24 * 60 * 60 * 1000) {
            // Award weekly bonus
            this.addCoins(userId, guildId, this.coinRates.weeklyBonus, 'weekly_auto', 'Automatic weekly bonus');

            this.db.prepare(`
                UPDATE economy_users
                SET last_weekly = CURRENT_TIMESTAMP
                WHERE user_id = ? AND guild_id = ?
            `).run(userId, guildId);

            console.log(`🎉 WEEKLY BONUS [${guildId}] [${userId}] +${this.coinRates.weeklyBonus} coins (automatic)`);
        }
        } catch (error) {
            console.error('Economy system error in checkAutomaticBonuses:', error);
        }
    }

    // Start automatic bonus checking (runs every hour)
    startAutomaticBonuses() {
        setInterval(() => {
            // Check all users for automatic bonuses
            const users = this.db.prepare(`
                SELECT user_id, guild_id FROM economy_users
                WHERE last_daily IS NULL OR last_weekly IS NULL
                OR datetime(last_daily) <= datetime('now', '-1 day')
                OR datetime(last_weekly) <= datetime('now', '-7 days')
            `).all();

            for (const user of users) {
                this.checkAutomaticBonuses(user.user_id, user.guild_id);
            }
        }, 60 * 60 * 1000); // Every hour

        console.log('🎁 Automatic bonus system started - checking every hour');
    }

    // Get leaderboard
    getLeaderboard(guildId, limit = 10) {
        return this.db.prepare(`
            SELECT user_id, coins, total_earned, messages_sent, voice_minutes
            FROM economy_users
            WHERE guild_id = ?
            ORDER BY coins DESC
            LIMIT ?
        `).all(guildId, limit);
    }

    // Get last work time
    getLastWork(userId, guildId) {
        try {
            const result = this.db.prepare(`
                SELECT last_work FROM economy_users
                WHERE user_id = ? AND guild_id = ?
            `).get(userId, guildId);

            return result && result.last_work ? new Date(result.last_work).getTime() : null;
        } catch (error) {
            return null;
        }
    }

    // Set last work time
    setLastWork(userId, guildId, timestamp) {
        this.getUser(userId, guildId); // Ensure user exists

        this.db.prepare(`
            UPDATE economy_users
            SET last_work = ?
            WHERE user_id = ? AND guild_id = ?
        `).run(new Date(timestamp).toISOString(), userId, guildId);
    }

}

module.exports = EconomySystem;
