const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er, Embed<PERSON><PERSON>er, PermissionFlagsBits } = require('discord.js');
const Database = require('../database/database');

module.exports = {
    data: {
        name: 'util',
        description: 'Utility commands',
    },
    slashData: new SlashCommandBuilder()
        .setName('util')
        .setDescription('Utility commands')
        .addSubcommand(subcommand =>
            subcommand
                .setName('userinfo')
                .setDescription('Get information about a user')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to get info about')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('serverinfo')
                .setDescription('Get information about the server'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('avatar')
                .setDescription('Get a user\'s avatar')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to get avatar of')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('ping')
                .setDescription('Check bot latency'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('help')
                .setDescription('Show help menu')),
    aliases: ['userinfo', 'serverinfo', 'avatar', 'ping', 'help'],
    cooldown: 3,
    async execute(interaction, args) {
        // Handle both slash commands and prefix commands
        if (interaction.isChatInputCommand && interaction.isChatInputCommand()) {
            // Slash command
            const subcommand = interaction.options.getSubcommand();
            return await this.handleSlashCommand(interaction, subcommand);
        } else {
            // Prefix command (message)
            const subcommand = args[0]?.toLowerCase();
            switch (subcommand) {
                case 'userinfo':
                    return await handleUserInfo(interaction, args.slice(1));
                case 'serverinfo':
                    return await handleServerInfo(interaction);
                case 'avatar':
                    return await handleAvatar(interaction, args.slice(1));
                case 'ping':
                    return await handlePing(interaction);
                case 'help':
                    return await handleHelp(interaction);
                default:
                    return await handleHelp(interaction);
            }
        }
    },

    async handleSlashCommand(interaction, subcommand) {
        switch (subcommand) {
            case 'userinfo':
                return await handleSlashUserInfo(interaction);
            case 'serverinfo':
                return await handleSlashServerInfo(interaction);
            case 'avatar':
                return await handleSlashAvatar(interaction);
            case 'ping':
                return await handleSlashPing(interaction);
            case 'help':
                return await handleSlashHelp(interaction);
        }
    },
};

async function handleUserInfo(message, args) {
    const user = message.mentions.users.first() || message.author;
    const member = message.guild.members.cache.get(user.id);

    if (!member) {
        const errorEmoji = global.emojiManager ? global.emojiManager.get('error') : '❌';
        return message.reply(`${errorEmoji} User not found in this server.`);
    }

    try {
        const userData = await Database.getUserXP(user.id, message.guild.id);

        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle(`User Info - ${user.tag}`)
            .setThumbnail(user.displayAvatarURL({ dynamic: true }))
            .addFields(
                { name: 'ID', value: user.id, inline: true },
                { name: 'Nickname', value: member.nickname || 'None', inline: true },
                { name: 'Account Created', value: `<t:${Math.floor(user.createdTimestamp / 1000)}:R>`, inline: true },
                { name: 'Joined Server', value: member.joinedAt ? `<t:${Math.floor(member.joinedAt.getTime() / 1000)}:R>` : 'Unknown', inline: true },
                { name: 'Level', value: userData.level.toString(), inline: true },
                { name: 'XP', value: userData.xp.toString(), inline: true },
                { name: 'Roles', value: member.roles.cache.filter(role => role.name !== '@everyone').map(role => role.toString()).join(', ') || 'None', inline: false }
            )
            .setFooter({ text: `Requested by ${message.author.tag}` })
            .setTimestamp();

        await message.reply({ embeds: [embed] });
    } catch (error) {
        console.error('UserInfo error:', error);
        const errorEmoji = global.emojiManager ? global.emojiManager.get('error') : '❌';
        await message.reply(`${errorEmoji} Failed to fetch user information.`);
    }
}

async function handleServerInfo(message) {
    const guild = message.guild;

    try {
        const owner = await guild.fetchOwner();

        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle(`Server Info - ${guild.name}`)
            .setThumbnail(guild.iconURL({ dynamic: true }))
            .addFields(
                { name: 'ID', value: guild.id, inline: true },
                { name: 'Owner', value: owner.user.tag, inline: true },
                { name: 'Created', value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:R>`, inline: true },
                { name: 'Members', value: guild.memberCount.toString(), inline: true },
                { name: 'Channels', value: guild.channels.cache.size.toString(), inline: true },
                { name: 'Roles', value: guild.roles.cache.size.toString(), inline: true },
                { name: 'Boost Level', value: guild.premiumTier.toString(), inline: true },
                { name: 'Boosts', value: guild.premiumSubscriptionCount?.toString() || '0', inline: true },
                { name: 'Verification Level', value: guild.verificationLevel.toString(), inline: true }
            )
            .setFooter({ text: `Requested by ${message.author.tag}` })
            .setTimestamp();

        await message.reply({ embeds: [embed] });
    } catch (error) {
        console.error('ServerInfo error:', error);
        await message.reply('❌ Failed to fetch server information.');
    }
}

async function handleAvatar(message, args) {
    const user = message.mentions.users.first() || message.author;

    const embed = new EmbedBuilder()
        .setColor(0x0099FF)
        .setTitle(`${user.tag}'s Avatar`)
        .setImage(user.displayAvatarURL({ dynamic: true, size: 512 }))
        .setFooter({ text: `Requested by ${message.author.tag}` });

    await message.reply({ embeds: [embed] });
}

async function handlePing(message) {
    const pingEmoji = global.emojiManager ? global.emojiManager.get('ping') : '🏓';
    const sent = await message.reply(`${pingEmoji} Pinging...`);
    const timeDiff = sent.createdTimestamp - message.createdTimestamp;

    const embed = new EmbedBuilder()
        .setColor(0x00FF00)
        .setTitle(`${pingEmoji} Pong!`)
        .addFields(
            { name: 'Bot Latency', value: `${timeDiff}ms`, inline: true },
            { name: 'API Latency', value: `${Math.round(message.client.ws.ping)}ms`, inline: true }
        );

    await sent.edit({ content: '', embeds: [embed] });
}

async function handleHelp(message) {
    const helpEmbed = global.embedBuilder.custom({
        color: global.embedBuilder.getColor('info'),
        title: 'Bot Commands & Features',
        emoji: global.emojiReplacer.getBook(),
        description: `Welcome to **${global.embedBuilder.getBranding().name}**! Here are all available commands and features:`,
        fields: [
            {
                name: `${global.emojiReplacer.getHammer()} Moderation`,
                value: '`/mod ban/kick/mute/warn/purge`\n`/antispam` - Configure anti-spam protection\nPowerful moderation tools for server management',
                inline: false
            },
            {
                name: `${global.emojiReplacer.getGear()} Utility`,
                value: '`/util userinfo/serverinfo/avatar/ping/help`\n`/botstatus` - View bot status and information\nUseful utility commands for information and help',
                inline: false
            },
            {
                name: `${global.emojiReplacer.getTrophy()} Leveling & Economy`,
                value: '`/level` and `/leaderboard`\n`/economy` - Manage server economy\nLeveling system and server rankings with rewards',
                inline: false
            },
            {
                name: `${global.emojiReplacer.getTicket()} Support System`,
                value: '`/ticket setup/stats/close`\nAdvanced ticket system for user assistance and support',
                inline: false
            },
            {
                name: `${global.emojiReplacer.getReaction()} Role Management`,
                value: '`/reactionroles` - Reaction role system\n`/autorole` - Auto role assignment\nAutomatic role management for new and existing members',
                inline: false
            },
            {
                name: `${global.emojiReplacer.getWelcome()} Welcome System`,
                value: '`/welcome` - Configure welcome/goodbye messages\nCustomizable welcome and goodbye system with auto-roles',
                inline: false
            },
            {
                name: `${global.emojiReplacer.getSettings()} Configuration`,
                value: '`/settings` - Configure bot settings\n`/logging` - Setup server logging\nComprehensive server configuration options',
                inline: false
            }
        ],
        footer: `${global.embedBuilder.getBranding().footer} • Use /<command> for detailed help`
    });

    await message.reply({ embeds: [helpEmbed] });
}

// Slash command handlers
async function handleSlashUserInfo(interaction) {
    try {
        // Defer reply to prevent timeout
        await interaction.deferReply();

        const user = interaction.options.getUser('user') || interaction.user;

        // Check if guild exists
        if (!interaction.guild) {
            return await interaction.editReply({ content: `${global.emojiReplacer ? global.emojiReplacer.getError() : '❌'} This command can only be used in a server.` });
        }

        let member;
        try {
            // Try to get member from cache first
            member = interaction.guild.members.cache.get(user.id);

            // If not in cache, try to fetch
            if (!member) {
                member = await interaction.guild.members.fetch(user.id);
            }
        } catch (error) {
            console.error('Failed to fetch member:', error);
            return await interaction.editReply({ content: '❌ User not found in this server.' });
        }

        if (!member) {
            return await interaction.editReply({ content: '❌ User not found in this server.' });
        }

        const userData = await Database.getUserXP(user.id, interaction.guild.id);

        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle(`User Info - ${user.tag}`)
            .setThumbnail(user.displayAvatarURL({ dynamic: true }))
            .addFields(
                { name: 'ID', value: user.id, inline: true },
                { name: 'Nickname', value: member.nickname || 'None', inline: true },
                { name: 'Account Created', value: `<t:${Math.floor(user.createdTimestamp / 1000)}:R>`, inline: true },
                { name: 'Joined Server', value: member.joinedAt ? `<t:${Math.floor(member.joinedAt.getTime() / 1000)}:R>` : 'Unknown', inline: true },
                { name: 'Level', value: userData.level.toString(), inline: true },
                { name: 'XP', value: userData.xp.toString(), inline: true },
                { name: 'Roles', value: member.roles.cache.filter(role => role.name !== '@everyone').map(role => role.toString()).join(', ') || 'None', inline: false }
            )
            .setFooter({ text: `Requested by ${interaction.user.tag}` })
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });
    } catch (error) {
        console.error('UserInfo error:', error);
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: '❌ Failed to fetch user information.', flags: 64 });
        } else {
            await interaction.editReply({ content: '❌ Failed to fetch user information.' });
        }
    }
}

async function handleSlashServerInfo(interaction) {
    try {
        // Defer reply to prevent timeout
        await interaction.deferReply();

        const guild = interaction.guild;
        if (!guild) {
            return await interaction.editReply({ content: '❌ Could not fetch server information.' });
        }

        let owner;
        try {
            owner = await guild.fetchOwner();
        } catch (error) {
            console.error('Failed to fetch owner:', error);
            owner = { user: { tag: 'Unknown', id: guild.ownerId || 'Unknown' } };
        }

        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle(`Server Info - ${guild.name}`)
            .setThumbnail(guild.iconURL({ dynamic: true }))
            .addFields(
                { name: 'ID', value: guild.id, inline: true },
                { name: 'Owner', value: owner.user.tag, inline: true },
                { name: 'Created', value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:R>`, inline: true },
                { name: 'Members', value: guild.memberCount.toString(), inline: true },
                { name: 'Channels', value: guild.channels.cache.size.toString(), inline: true },
                { name: 'Roles', value: guild.roles.cache.size.toString(), inline: true },
                { name: 'Boost Level', value: guild.premiumTier.toString(), inline: true },
                { name: 'Boosts', value: guild.premiumSubscriptionCount?.toString() || '0', inline: true },
                { name: 'Verification Level', value: guild.verificationLevel.toString(), inline: true }
            )
            .setFooter({ text: `Requested by ${interaction.user.tag}` })
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });
    } catch (error) {
        console.error('ServerInfo error:', error);
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: '❌ Failed to fetch server information.', flags: 64 });
        } else {
            await interaction.editReply({ content: '❌ Failed to fetch server information.' });
        }
    }
}

async function handleSlashAvatar(interaction) {
    const user = interaction.options.getUser('user') || interaction.user;

    const embed = new EmbedBuilder()
        .setColor(0x0099FF)
        .setTitle(`${user.tag}'s Avatar`)
        .setImage(user.displayAvatarURL({ dynamic: true, size: 512 }))
        .setFooter({ text: `Requested by ${interaction.user.tag}` });

    await interaction.reply({ embeds: [embed] });
}

async function handleSlashPing(interaction) {
    await interaction.deferReply();

    const embed = new EmbedBuilder()
        .setColor(0x00FF00)
        .setTitle(`${global.emojiReplacer ? global.emojiReplacer.getPing() : '🏓'} Pong!`)
        .addFields(
            { name: 'API Latency', value: `${Math.round(interaction.client.ws.ping)}ms`, inline: true }
        );

    await interaction.editReply({ embeds: [embed] });
}

async function handleSlashHelp(interaction) {
    const helpEmbed = global.embedBuilder.custom({
        color: global.embedBuilder.getColor('info'),
        title: 'Bot Commands & Features',
        emoji: global.emojiReplacer.getBook(),
        description: `Welcome to **${global.embedBuilder.getBranding().name}**! Here are all available commands and features:`,
        fields: [
            {
                name: `${global.emojiReplacer.getHammer()} Moderation`,
                value: '`/mod ban/kick/mute/warn/purge`\n`/antispam` - Configure anti-spam protection\nPowerful moderation tools for server management',
                inline: false
            },
            {
                name: `${global.emojiReplacer.getGear()} Utility`,
                value: '`/util userinfo/serverinfo/avatar/ping/help`\n`/botstatus` - View bot status and information\nUseful utility commands for information and help',
                inline: false
            },
            {
                name: `${global.emojiReplacer.getTrophy()} Leveling & Economy`,
                value: '`/level` and `/leaderboard`\n`/economy` - Manage server economy\nLeveling system and server rankings with rewards',
                inline: false
            },
            {
                name: `${global.emojiReplacer.getTicket()} Support System`,
                value: '`/ticket setup/stats/close`\nAdvanced ticket system for user assistance and support',
                inline: false
            },
            {
                name: `${global.emojiReplacer.getReaction()} Role Management`,
                value: '`/reactionroles` - Reaction role system\n`/autorole` - Auto role assignment\nAutomatic role management for new and existing members',
                inline: false
            },
            {
                name: `${global.emojiReplacer.getWelcome()} Welcome System`,
                value: '`/welcome` - Configure welcome/goodbye messages\nCustomizable welcome and goodbye system with auto-roles',
                inline: false
            },
            {
                name: `${global.emojiReplacer.getSettings()} Configuration`,
                value: '`/settings` - Configure bot settings\n`/logging` - Setup server logging\nComprehensive server configuration options',
                inline: false
            }
        ],
        footer: `${global.embedBuilder.getBranding().footer} • Use /<command> for detailed help`
    });

    await interaction.reply({ embeds: [helpEmbed] });
}