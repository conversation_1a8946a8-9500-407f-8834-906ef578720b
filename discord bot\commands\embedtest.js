const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('embedtest')
        .setDescription('Test all embed styles and Shanta Somali branding')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .setDMPermission(false),

    async execute(interaction) {
        await interaction.deferReply();

        try {
            // Create main showcase embed
            const showcaseEmbed = global.embedBuilder.custom({
                color: global.embedBuilder.getColor('primary'),
                title: 'Embed System Showcase',
                emoji: global.emojiReplacer.getSparkles(),
                description: `**${global.embedBuilder.getBranding().name}** - Complete embed system demonstration\n\n${global.emojiReplacer.getShield()} All embeds feature consistent **Shanta Somali** branding\n${global.emojiReplacer.getSparkles()} Beautiful colors and professional design\n${global.emojiReplacer.getGear()} Custom emoji integration throughout`,
                fields: [
                    { 
                        name: `${global.emojiReplacer.getSuccess()} Success Style`, 
                        value: 'Green embeds for confirmations & positive feedback', 
                        inline: true 
                    },
                    { 
                        name: `${global.emojiReplacer.getError()} Error Style`, 
                        value: 'Red embeds for error messages & failures', 
                        inline: true 
                    },
                    { 
                        name: `${global.emojiReplacer.getWarning()} Warning Style`, 
                        value: 'Orange embeds for warnings & cautions', 
                        inline: true 
                    },
                    { 
                        name: `${global.emojiReplacer.getInfo()} Info Style`, 
                        value: 'Blue embeds for information & guides', 
                        inline: true 
                    },
                    { 
                        name: `${global.emojiReplacer.getCrown()} Premium Style`, 
                        value: 'Gold embeds for premium features', 
                        inline: true 
                    },
                    { 
                        name: `${global.emojiReplacer.getShield()} Primary Style`, 
                        value: 'Teal embeds for main brand features', 
                        inline: true 
                    }
                ],
                thumbnail: interaction.client.user.displayAvatarURL({ size: 256 }),
                footer: `${global.embedBuilder.getBranding().footer} • Embed System v2.0`
            });

            await interaction.editReply({ 
                content: `${global.emojiReplacer.getSparkles()} **Shanta Somali Embed System**\n\nWatch as different embed styles are demonstrated below:`,
                embeds: [showcaseEmbed] 
            });

            // Show success example
            setTimeout(async () => {
                const successEmbed = global.embedBuilder.success(
                    'Success Embed Example',
                    'This is how success messages look - clean, positive, and professionally branded!',
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getUser()} User`, value: interaction.user.toString(), inline: true },
                            { name: `${global.emojiReplacer.getServer()} Server`, value: interaction.guild.name, inline: true },
                            { name: `${global.emojiReplacer.getSparkles()} Status`, value: 'Perfect!', inline: true }
                        ]
                    }
                );
                await interaction.followUp({ embeds: [successEmbed] });
            }, 1500);

            // Show error example
            setTimeout(async () => {
                const errorEmbed = global.embedBuilder.error(
                    'Error Embed Example',
                    'Professional error handling with clear messaging and consistent branding.',
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getError()} Error Type`, value: 'Demo Error', inline: true },
                            { name: `${global.emojiReplacer.getInfo()} Solution`, value: 'This is just a test!', inline: true }
                        ]
                    }
                );
                await interaction.followUp({ embeds: [errorEmbed] });
            }, 3000);

            // Show premium example
            setTimeout(async () => {
                const premiumEmbed = global.embedBuilder.premium(
                    'Premium Embed Example',
                    'Elegant gold styling for premium features and special content.',
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getCrown()} Premium Status`, value: 'Active', inline: true },
                            { name: `${global.emojiReplacer.getGem()} Benefits`, value: 'Exclusive features', inline: true },
                            { name: `${global.emojiReplacer.getSparkles()} Quality`, value: 'Professional', inline: true }
                        ]
                    }
                );
                await interaction.followUp({ embeds: [premiumEmbed] });
            }, 4500);

            // Show economy example
            setTimeout(async () => {
                const economyEmbed = global.embedBuilder.economy(
                    'Economy Embed Example',
                    'Beautiful economy styling for coins, rewards, and financial features.',
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getCoin()} Balance`, value: '1,250 coins', inline: true },
                            { name: `${global.emojiReplacer.getTrophy()} Rank`, value: '#15', inline: true },
                            { name: `${global.emojiReplacer.getGift()} Reward`, value: 'Available', inline: true }
                        ]
                    }
                );
                await interaction.followUp({ embeds: [economyEmbed] });
            }, 6000);

            // Show welcome example
            setTimeout(async () => {
                const welcomeEmbed = global.embedBuilder.welcome(
                    'Welcome Embed Example',
                    `Welcome to **${interaction.guild.name}**! This is how our welcome messages look with beautiful branding.`,
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getUser()} New Member`, value: interaction.user.tag, inline: true },
                            { name: `${global.emojiReplacer.getParty()} Members`, value: `${interaction.guild.memberCount}`, inline: true },
                            { name: `${global.emojiReplacer.getSparkles()} Welcome!`, value: 'Enjoy your stay!', inline: true }
                        ],
                        thumbnail: interaction.user.displayAvatarURL()
                    }
                );
                await interaction.followUp({ embeds: [welcomeEmbed] });
            }, 7500);

            // Final summary
            setTimeout(async () => {
                const summaryEmbed = global.embedBuilder.custom({
                    color: global.embedBuilder.getColor('success'),
                    title: 'Embed Test Complete!',
                    emoji: global.emojiReplacer.getSuccess(),
                    description: `${global.emojiReplacer.getSparkles()} **All embed styles demonstrated successfully!**\n\n${global.emojiReplacer.getShield()} Consistent **Shanta Somali** branding\n${global.emojiReplacer.getGear()} Professional design system\n${global.emojiReplacer.getCrown()} Production-ready quality`,
                    fields: [
                        { 
                            name: `${global.emojiReplacer.getChart()} System Status`, 
                            value: 'All embed types working perfectly!', 
                            inline: false 
                        },
                        { 
                            name: `${global.emojiReplacer.getInfo()} Next Steps`, 
                            value: 'Ready for production use across all bot features', 
                            inline: false 
                        }
                    ]
                });
                await interaction.followUp({ embeds: [summaryEmbed] });
            }, 9000);

        } catch (error) {
            console.error('Error in embed test:', error);
            const errorEmbed = global.embedBuilder.error(
                'Test Failed',
                'An error occurred during the embed test.',
                {
                    fields: [
                        { name: `${global.emojiReplacer.getError()} Error`, value: error.message, inline: false }
                    ]
                }
            );
            await interaction.editReply({ embeds: [errorEmbed] });
        }
    }
};
