const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('embedtest')
        .setDescription('Test all embed styles and branding')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .setDMPermission(false)
        .addStringOption(option =>
            option.setName('type')
                .setDescription('Type of embed to test')
                .setRequired(true)
                .addChoices(
                    { name: 'Success Embed', value: 'success' },
                    { name: 'Error Embed', value: 'error' },
                    { name: 'Warning Embed', value: 'warning' },
                    { name: 'Info Embed', value: 'info' },
                    { name: 'Primary Embed', value: 'primary' },
                    { name: 'Premium Embed', value: 'premium' },
                    { name: 'Economy Embed', value: 'economy' },
                    { name: 'Moderation Embed', value: 'moderation' },
                    { name: 'Ticket Embed', value: 'ticket' },
                    { name: 'Welcome Embed', value: 'welcome' },
                    { name: 'All Embeds', value: 'all' }
                )),

    async execute(interaction) {
        const embedType = interaction.options.getString('type');

        if (embedType === 'all') {
            await this.showAllEmbeds(interaction);
        } else {
            await this.showSingleEmbed(interaction, embedType);
        }
    },

    async showSingleEmbed(interaction, type) {
        await interaction.deferReply();

        let embed;
        const sampleFields = [
            { name: `${global.emojiReplacer.getUser()} User`, value: `${interaction.user}`, inline: true },
            { name: `${global.emojiReplacer.getServer()} Server`, value: interaction.guild.name, inline: true },
            { name: `${global.emojiReplacer.getTime()} Time`, value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: false }
        ];

        switch (type) {
            case 'success':
                embed = global.embedBuilder.success(
                    'Success Embed Test',
                    'This is a beautiful success embed with Shanta Somali branding! Perfect for confirmations and positive feedback.',
                    {
                        fields: sampleFields,
                        thumbnail: interaction.user.displayAvatarURL()
                    }
                );
                break;

            case 'error':
                embed = global.embedBuilder.error(
                    'Error Embed Test',
                    'This is a professional error embed. Clear, informative, and maintains brand consistency.',
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getError()} Error Code`, value: 'TEST_ERROR_001', inline: true },
                            { name: `${global.emojiReplacer.getInfo()} Solution`, value: 'This is just a test - no real error occurred!', inline: false }
                        ]
                    }
                );
                break;

            case 'warning':
                embed = global.embedBuilder.warning(
                    'Warning Embed Test',
                    'This warning embed catches attention while maintaining professional appearance.',
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getWarning()} Warning Type`, value: 'Test Warning', inline: true },
                            { name: `${global.emojiReplacer.getInfo()} Action Required`, value: 'No action needed - this is just a test!', inline: false }
                        ]
                    }
                );
                break;

            case 'info':
                embed = global.embedBuilder.info(
                    'Information Embed Test',
                    'Clean, informative embed perfect for displaying helpful information and guides.',
                    {
                        fields: sampleFields
                    }
                );
                break;

            case 'primary':
                embed = global.embedBuilder.primary(
                    'Primary Brand Embed Test',
                    'This embed uses our main brand color - perfect for important announcements and features.',
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getShield()} Brand Color`, value: 'Teal (#00AE86)', inline: true },
                            { name: `${global.emojiReplacer.getSparkles()} Usage`, value: 'Main features and announcements', inline: true }
                        ]
                    }
                );
                break;

            case 'premium':
                embed = global.embedBuilder.premium(
                    'Premium Feature Test',
                    'Elegant gold embed for premium features and special content.',
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getCrown()} Premium Status`, value: 'Active', inline: true },
                            { name: `${global.emojiReplacer.getGem()} Benefits`, value: 'Exclusive features and priority support', inline: false }
                        ]
                    }
                );
                break;

            case 'economy':
                embed = global.embedBuilder.economy(
                    'Economy System Test',
                    'Beautiful economy embed for coins, rewards, and financial features.',
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getCoin()} Balance`, value: '1,250 coins', inline: true },
                            { name: `${global.emojiReplacer.getTrophy()} Rank`, value: '#15 on leaderboard', inline: true },
                            { name: `${global.emojiReplacer.getGift()} Daily Reward`, value: 'Available in 2 hours', inline: false }
                        ]
                    }
                );
                break;

            case 'moderation':
                embed = global.embedBuilder.moderation(
                    'Moderation Action Test',
                    'Professional moderation embed for disciplinary actions and server management.',
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getHammer()} Action`, value: 'Test Action', inline: true },
                            { name: `${global.emojiReplacer.getUser()} Target`, value: interaction.user.tag, inline: true },
                            { name: `${global.emojiReplacer.getShield()} Moderator`, value: 'System Test', inline: true },
                            { name: `${global.emojiReplacer.getInfo()} Reason`, value: 'Embed testing purposes only', inline: false }
                        ]
                    }
                );
                break;

            case 'ticket':
                embed = global.embedBuilder.ticket(
                    'Support Ticket Test',
                    'Clean ticket embed for support system and user assistance.',
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getTicket()} Ticket ID`, value: 'TEST-001', inline: true },
                            { name: `${global.emojiReplacer.getUser()} Creator`, value: interaction.user.tag, inline: true },
                            { name: `${global.emojiReplacer.getTime()} Status`, value: 'Open', inline: true },
                            { name: `${global.emojiReplacer.getInfo()} Category`, value: 'Embed Testing', inline: false }
                        ]
                    }
                );
                break;

            case 'welcome':
                embed = global.embedBuilder.welcome(
                    'Welcome Message Test',
                    `Welcome to **${interaction.guild.name}**! This is how our welcome messages look.`,
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getUser()} New Member`, value: interaction.user.tag, inline: true },
                            { name: `${global.emojiReplacer.getServer()} Server`, value: interaction.guild.name, inline: true },
                            { name: `${global.emojiReplacer.getParty()} Member Count`, value: `${interaction.guild.memberCount} members`, inline: true }
                        ],
                        thumbnail: interaction.user.displayAvatarURL()
                    }
                );
                break;
        }

        await interaction.editReply({ embeds: [embed] });
    },

    async showAllEmbeds(interaction) {
        await interaction.deferReply();

        // Create a showcase embed first
        const showcaseEmbed = global.embedBuilder.custom({
            color: global.embedBuilder.getColor('primary'),
            title: 'Embed Showcase - All Styles',
            emoji: global.emojiReplacer.getSparkles(),
            description: `**${global.embedBuilder.getBranding().name}** - Complete embed system demonstration\n\nAll embeds feature consistent branding, beautiful colors, and professional design.`,
            fields: [
                { name: `${global.emojiReplacer.getSuccess()} Success`, value: 'Green - Confirmations & positive feedback', inline: true },
                { name: `${global.emojiReplacer.getError()} Error`, value: 'Red - Error messages & failures', inline: true },
                { name: `${global.emojiReplacer.getWarning()} Warning`, value: 'Orange - Warnings & cautions', inline: true },
                { name: `${global.emojiReplacer.getInfo()} Info`, value: 'Blue - Information & guides', inline: true },
                { name: `${global.emojiReplacer.getShield()} Primary`, value: 'Teal - Main brand features', inline: true },
                { name: `${global.emojiReplacer.getCrown()} Premium`, value: 'Gold - Premium features', inline: true },
                { name: `${global.emojiReplacer.getCoin()} Economy`, value: 'Gold - Financial features', inline: true },
                { name: `${global.emojiReplacer.getHammer()} Moderation`, value: 'Dark - Moderation actions', inline: true },
                { name: `${global.emojiReplacer.getTicket()} Tickets`, value: 'Blue - Support system', inline: true }
            ],
            footer: `${global.embedBuilder.getBranding().footer} • Embed System v2.0`
        });

        await interaction.editReply({ 
            content: `${global.emojiReplacer.getSparkles()} **Embed System Showcase**\n\nUse the dropdown below to test individual embed types:`,
            embeds: [showcaseEmbed] 
        });

        // Send individual examples
        setTimeout(async () => {
            const successEmbed = global.embedBuilder.success(
                'Success Example',
                'This is how success messages look - clean and positive!'
            );
            await interaction.followUp({ embeds: [successEmbed] });
        }, 1000);

        setTimeout(async () => {
            const errorEmbed = global.embedBuilder.error(
                'Error Example', 
                'Professional error handling with clear messaging.'
            );
            await interaction.followUp({ embeds: [errorEmbed] });
        }, 2000);

        setTimeout(async () => {
            const premiumEmbed = global.embedBuilder.premium(
                'Premium Example',
                'Elegant gold styling for premium features.'
            );
            await interaction.followUp({ embeds: [premiumEmbed] });
        }, 3000);
    }
};
