const { EmbedBuilder, PermissionFlagsBits, ChannelType } = require('discord.js');
const Database = require('../database/database');

class LockdownSystem {
    constructor() {
        this.activeLockdowns = new Map(); // guildId -> lockdown data
        this.permissionBackups = new Map(); // guildId -> backup data
        this.safelists = new Map(); // guildId -> { roles: Set, users: Set }
    }

    // Main lockdown activation
    async activateLockdown(interaction, reason) {
        const guild = interaction.guild;
        const guildId = guild.id;

        // Check if lockdown is already active
        if (this.activeLockdowns.has(guildId)) {
            return await interaction.reply({
                content: '🚨 Lockdown is already active in this server!',
                ephemeral: true
            });
        }

        await interaction.deferReply();

        try {
            // Initialize safelist for this guild
            if (!this.safelists.has(guildId)) {
                this.safelists.set(guildId, { roles: new Set(), users: new Set() });
            }

            // Backup current permissions
            await this.backupPermissions(guild);

            // Apply lockdown to all channels and categories
            await this.lockAllChannels(guild);

            // Create lockdown info channel
            const infoChannel = await this.createLockdownInfoChannel(guild, reason, interaction.user);

            // Store lockdown data
            this.activeLockdowns.set(guildId, {
                reason,
                activatedBy: interaction.user.id,
                activatedAt: Date.now(),
                infoChannelId: infoChannel.id
            });

            // Log to database
            await Database.logAnalytics(guildId, 'lockdown_activated', {
                reason,
                activated_by: interaction.user.id
            }, interaction.user.id);

            const emojis = global.emojiReplacer || {};
            await interaction.editReply({
                content: `${emojis.getShield ? emojis.getShield() : '🛡️'} **Server Lockdown Activated**\n\n**Reason:** ${reason}\n**Info Channel:** ${infoChannel}\n\nUse \`/lockdown allow\` to grant access to specific users/roles.`
            });

        } catch (error) {
            console.error('Lockdown activation error:', error);
            await interaction.editReply({
                content: '❌ Failed to activate lockdown. Please check bot permissions.'
            });
        }
    }

    // Backup all channel and category permissions
    async backupPermissions(guild) {
        const guildId = guild.id;
        const backup = {
            channels: new Map(),
            categories: new Map()
        };

        // Backup channel permissions
        for (const [channelId, channel] of guild.channels.cache) {
            if (channel.type === ChannelType.GuildText || 
                channel.type === ChannelType.GuildVoice || 
                channel.type === ChannelType.GuildStageVoice ||
                channel.type === ChannelType.GuildForum ||
                channel.type === ChannelType.PublicThread ||
                channel.type === ChannelType.PrivateThread) {
                
                backup.channels.set(channelId, {
                    permissionOverwrites: channel.permissionOverwrites.cache.map(overwrite => ({
                        id: overwrite.id,
                        type: overwrite.type,
                        allow: overwrite.allow.bitfield.toString(),
                        deny: overwrite.deny.bitfield.toString()
                    }))
                });
            }
            
            // Backup category permissions
            if (channel.type === ChannelType.GuildCategory) {
                backup.categories.set(channelId, {
                    permissionOverwrites: channel.permissionOverwrites.cache.map(overwrite => ({
                        id: overwrite.id,
                        type: overwrite.type,
                        allow: overwrite.allow.bitfield.toString(),
                        deny: overwrite.deny.bitfield.toString()
                    }))
                });
            }
        }

        this.permissionBackups.set(guildId, backup);
        console.log(`🔒 Backed up permissions for ${backup.channels.size} channels and ${backup.categories.size} categories`);
    }

    // Lock all channels and categories
    async lockAllChannels(guild) {
        const guildId = guild.id;
        const safelist = this.safelists.get(guildId);
        let lockedCount = 0;

        for (const [channelId, channel] of guild.channels.cache) {
            try {
                if (channel.type === ChannelType.GuildText || 
                    channel.type === ChannelType.GuildVoice || 
                    channel.type === ChannelType.GuildStageVoice ||
                    channel.type === ChannelType.GuildForum ||
                    channel.type === ChannelType.GuildCategory) {
                    
                    // Create new permission overwrites
                    const newOverwrites = [];

                    // Deny @everyone
                    newOverwrites.push({
                        id: guild.roles.everyone.id,
                        deny: [PermissionFlagsBits.ViewChannel]
                    });

                    // Allow bot
                    newOverwrites.push({
                        id: guild.members.me.id,
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.ManageChannels,
                            PermissionFlagsBits.ManageRoles
                        ]
                    });

                    // Allow safelisted roles
                    for (const roleId of safelist.roles) {
                        const role = guild.roles.cache.get(roleId);
                        if (role) {
                            newOverwrites.push({
                                id: roleId,
                                allow: [PermissionFlagsBits.ViewChannel]
                            });
                        }
                    }

                    // Allow safelisted users
                    for (const userId of safelist.users) {
                        newOverwrites.push({
                            id: userId,
                            allow: [PermissionFlagsBits.ViewChannel]
                        });
                    }

                    // Apply permissions
                    await channel.permissionOverwrites.set(newOverwrites, 'Server Lockdown');
                    lockedCount++;
                }
            } catch (error) {
                console.error(`Failed to lock channel ${channel.name}:`, error);
            }
        }

        console.log(`🔒 Locked ${lockedCount} channels/categories`);
    }

    // Create lockdown info channel
    async createLockdownInfoChannel(guild, reason, activatedBy) {
        const guildId = guild.id;
        const safelist = this.safelists.get(guildId);
        const emojis = global.emojiReplacer || {};

        // Create channel
        const infoChannel = await guild.channels.create({
            name: 'lockdown-info',
            type: ChannelType.GuildText,
            topic: 'Server lockdown information and status',
            permissionOverwrites: [
                {
                    id: guild.roles.everyone.id,
                    allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.ReadMessageHistory],
                    deny: [PermissionFlagsBits.SendMessages]
                },
                {
                    id: guild.members.me.id,
                    allow: [
                        PermissionFlagsBits.ViewChannel,
                        PermissionFlagsBits.SendMessages,
                        PermissionFlagsBits.ManageChannels
                    ]
                }
            ]
        });

        // Create embed
        const embed = new EmbedBuilder()
            .setColor(0xFF0000)
            .setTitle(`${emojis.getShield ? emojis.getShield() : '🚨'} Server Lockdown Active`)
            .setDescription('This server is currently under lockdown. Access is restricted to authorized users and roles only.')
            .addFields(
                {
                    name: `${emojis.getReason ? emojis.getReason() : '📝'} Reason`,
                    value: reason,
                    inline: false
                },
                {
                    name: `${emojis.getUser ? emojis.getUser() : '👤'} Activated By`,
                    value: `${activatedBy} (${activatedBy.tag})`,
                    inline: true
                },
                {
                    name: `${emojis.getTime ? emojis.getTime() : '⏰'} Status`,
                    value: `Active since: <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            )
            .setFooter({ text: 'Use /lockdown allow to grant access • /lockdown off to deactivate' })
            .setTimestamp();

        // Add safelist info if any
        if (safelist.roles.size > 0 || safelist.users.size > 0) {
            let allowedText = '';
            
            if (safelist.roles.size > 0) {
                const roleNames = Array.from(safelist.roles).map(roleId => {
                    const role = guild.roles.cache.get(roleId);
                    return role ? `<@&${roleId}>` : 'Unknown Role';
                }).join(', ');
                allowedText += `**Roles:** ${roleNames}\n`;
            }
            
            if (safelist.users.size > 0) {
                const userNames = Array.from(safelist.users).map(userId => `<@${userId}>`).join(', ');
                allowedText += `**Users:** ${userNames}`;
            }

            embed.addFields({
                name: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} Allowed Access`,
                value: allowedText || 'None',
                inline: false
            });
        }

        await infoChannel.send({ embeds: [embed] });
        return infoChannel;
    }

    // Add user or role to safelist
    async allowAccess(interaction, target) {
        const guild = interaction.guild;
        const guildId = guild.id;

        // Check if lockdown is active
        if (!this.activeLockdowns.has(guildId)) {
            return await interaction.reply({
                content: '❌ No active lockdown found in this server.',
                ephemeral: true
            });
        }

        const safelist = this.safelists.get(guildId);
        const emojis = global.emojiReplacer || {};
        let targetType, targetId, targetName;

        // Determine if target is user or role
        if (target.user) {
            // It's a user
            targetType = 'user';
            targetId = target.id;
            targetName = target.displayName;

            if (safelist.users.has(targetId)) {
                return await interaction.reply({
                    content: `${emojis.getWarning ? emojis.getWarning() : '⚠️'} ${targetName} is already allowed access.`,
                    ephemeral: true
                });
            }

            safelist.users.add(targetId);
        } else {
            // It's a role
            targetType = 'role';
            targetId = target.id;
            targetName = target.name;

            if (safelist.roles.has(targetId)) {
                return await interaction.reply({
                    content: `${emojis.getWarning ? emojis.getWarning() : '⚠️'} Role ${targetName} is already allowed access.`,
                    ephemeral: true
                });
            }

            safelist.roles.add(targetId);
        }

        try {
            // Update permissions for all channels
            await this.updateChannelPermissions(guild, targetId, targetType, 'allow');

            // Update info channel
            await this.updateLockdownInfoChannel(guild);

            // Log to database
            await Database.logAnalytics(guildId, 'lockdown_access_granted', {
                target_type: targetType,
                target_id: targetId,
                granted_by: interaction.user.id
            }, interaction.user.id);

            await interaction.reply({
                content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Access Granted**\n\n${targetType === 'user' ? `User ${targetName}` : `Role ${targetName}`} can now access channels during lockdown.`
            });

        } catch (error) {
            console.error('Error granting access:', error);
            await interaction.reply({
                content: '❌ Failed to grant access. Please check bot permissions.',
                ephemeral: true
            });
        }
    }

    // Update channel permissions for specific target
    async updateChannelPermissions(guild, targetId, targetType, action) {
        for (const [channelId, channel] of guild.channels.cache) {
            try {
                if (channel.type === ChannelType.GuildText ||
                    channel.type === ChannelType.GuildVoice ||
                    channel.type === ChannelType.GuildStageVoice ||
                    channel.type === ChannelType.GuildForum ||
                    channel.type === ChannelType.GuildCategory) {

                    if (action === 'allow') {
                        await channel.permissionOverwrites.edit(targetId, {
                            ViewChannel: true
                        }, 'Lockdown access granted');
                    } else if (action === 'deny') {
                        await channel.permissionOverwrites.edit(targetId, {
                            ViewChannel: false
                        }, 'Lockdown access revoked');
                    }
                }
            } catch (error) {
                console.error(`Failed to update permissions for channel ${channel.name}:`, error);
            }
        }
    }

    // Update lockdown info channel
    async updateLockdownInfoChannel(guild) {
        const guildId = guild.id;
        const lockdownData = this.activeLockdowns.get(guildId);
        const safelist = this.safelists.get(guildId);
        const emojis = global.emojiReplacer || {};

        if (!lockdownData) return;

        try {
            const infoChannel = guild.channels.cache.get(lockdownData.infoChannelId);
            if (!infoChannel) return;

            const messages = await infoChannel.messages.fetch({ limit: 10 });
            const botMessage = messages.find(msg => msg.author.id === guild.members.me.id && msg.embeds.length > 0);

            if (botMessage) {
                const embed = new EmbedBuilder()
                    .setColor(0xFF0000)
                    .setTitle(`${emojis.getShield ? emojis.getShield() : '🚨'} Server Lockdown Active`)
                    .setDescription('This server is currently under lockdown. Access is restricted to authorized users and roles only.')
                    .addFields(
                        {
                            name: `${emojis.getReason ? emojis.getReason() : '📝'} Reason`,
                            value: lockdownData.reason,
                            inline: false
                        },
                        {
                            name: `${emojis.getUser ? emojis.getUser() : '👤'} Activated By`,
                            value: `<@${lockdownData.activatedBy}>`,
                            inline: true
                        },
                        {
                            name: `${emojis.getTime ? emojis.getTime() : '⏰'} Status`,
                            value: `Active since: <t:${Math.floor(lockdownData.activatedAt / 1000)}:F>`,
                            inline: true
                        }
                    )
                    .setFooter({ text: 'Use /lockdown allow to grant access • /lockdown off to deactivate' })
                    .setTimestamp();

                // Add safelist info
                if (safelist.roles.size > 0 || safelist.users.size > 0) {
                    let allowedText = '';

                    if (safelist.roles.size > 0) {
                        const roleNames = Array.from(safelist.roles).map(roleId => {
                            const role = guild.roles.cache.get(roleId);
                            return role ? `<@&${roleId}>` : 'Unknown Role';
                        }).join(', ');
                        allowedText += `**Roles:** ${roleNames}\n`;
                    }

                    if (safelist.users.size > 0) {
                        const userNames = Array.from(safelist.users).map(userId => `<@${userId}>`).join(', ');
                        allowedText += `**Users:** ${userNames}`;
                    }

                    embed.addFields({
                        name: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} Allowed Access`,
                        value: allowedText || 'None',
                        inline: false
                    });
                }

                await botMessage.edit({ embeds: [embed] });
            }
        } catch (error) {
            console.error('Failed to update lockdown info channel:', error);
        }
    }

    // Deactivate lockdown
    async deactivateLockdown(interaction) {
        const guild = interaction.guild;
        const guildId = guild.id;

        // Check if lockdown is active
        if (!this.activeLockdowns.has(guildId)) {
            return await interaction.reply({
                content: '❌ No active lockdown found in this server.',
                ephemeral: true
            });
        }

        await interaction.deferReply();

        try {
            const lockdownData = this.activeLockdowns.get(guildId);
            const emojis = global.emojiReplacer || {};

            // Restore permissions from backup
            await this.restorePermissions(guild);

            // Delete lockdown info channel
            const infoChannel = guild.channels.cache.get(lockdownData.infoChannelId);
            if (infoChannel) {
                await infoChannel.delete('Lockdown deactivated');
            }

            // Clean up data
            this.activeLockdowns.delete(guildId);
            this.permissionBackups.delete(guildId);
            this.safelists.delete(guildId);

            // Calculate lockdown duration
            const duration = Date.now() - lockdownData.activatedAt;
            const durationMinutes = Math.floor(duration / (1000 * 60));

            // Log to database
            await Database.logAnalytics(guildId, 'lockdown_deactivated', {
                duration_minutes: durationMinutes,
                deactivated_by: interaction.user.id
            }, interaction.user.id);

            await interaction.editReply({
                content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Lockdown Deactivated**\n\nServer permissions have been restored to their previous state.\n**Duration:** ${durationMinutes} minutes`
            });

        } catch (error) {
            console.error('Lockdown deactivation error:', error);
            await interaction.editReply({
                content: '❌ Failed to deactivate lockdown. Some permissions may need manual restoration.'
            });
        }
    }

    // Restore permissions from backup
    async restorePermissions(guild) {
        const guildId = guild.id;
        const backup = this.permissionBackups.get(guildId);

        if (!backup) {
            console.error('No permission backup found for guild:', guildId);
            return;
        }

        let restoredCount = 0;

        // Restore channel permissions
        for (const [channelId, channelData] of backup.channels) {
            try {
                const channel = guild.channels.cache.get(channelId);
                if (channel) {
                    const overwrites = channelData.permissionOverwrites.map(overwrite => ({
                        id: overwrite.id,
                        type: overwrite.type,
                        allow: BigInt(overwrite.allow),
                        deny: BigInt(overwrite.deny)
                    }));

                    await channel.permissionOverwrites.set(overwrites, 'Lockdown deactivated - restoring permissions');
                    restoredCount++;
                }
            } catch (error) {
                console.error(`Failed to restore permissions for channel ${channelId}:`, error);
            }
        }

        // Restore category permissions
        for (const [categoryId, categoryData] of backup.categories) {
            try {
                const category = guild.channels.cache.get(categoryId);
                if (category) {
                    const overwrites = categoryData.permissionOverwrites.map(overwrite => ({
                        id: overwrite.id,
                        type: overwrite.type,
                        allow: BigInt(overwrite.allow),
                        deny: BigInt(overwrite.deny)
                    }));

                    await category.permissionOverwrites.set(overwrites, 'Lockdown deactivated - restoring permissions');
                    restoredCount++;
                }
            } catch (error) {
                console.error(`Failed to restore permissions for category ${categoryId}:`, error);
            }
        }

        console.log(`🔓 Restored permissions for ${restoredCount} channels/categories`);
    }

    // Get lockdown status
    getLockdownStatus(guildId) {
        return this.activeLockdowns.get(guildId) || null;
    }

    // Check if lockdown is active
    isLockdownActive(guildId) {
        return this.activeLockdowns.has(guildId);
    }

    // Get safelist for guild
    getSafelist(guildId) {
        return this.safelists.get(guildId) || { roles: new Set(), users: new Set() };
    }
}

module.exports = LockdownSystem;
