const { EmbedBuilder } = require('discord.js');

class CustomEmbedBuilder {
    constructor() {
        // Brand colors
        this.colors = {
            primary: 0x00AE86,      // Teal - main brand color
            success: 0x00FF7F,      // Green - success messages
            error: 0xFF4757,        // Red - error messages
            warning: 0xFFA502,      // Orange - warning messages
            info: 0x3742FA,         // Blue - info messages
            premium: 0xFFD700,      // Gold - premium features
            purple: 0x8E44AD,       // Purple - special features
            dark: 0x2C2C54,         // Dark - serious messages
            light: 0xF1F2F6         // Light - neutral messages
        };

        // Bot branding
        this.branding = {
            name: "<PERSON><PERSON> Bot",
            logo: "https://cdn.discordapp.com/attachments/1234567890/shanta-logo.png", // Replace with actual logo
            website: "https://shantasomali.com",
            footer: "Powered by Shan<PERSON> Somali • Advanced Discord Bot"
        };

        // Get emojis from global emoji replacer
        this.emojis = global.emojiReplacer || {};
    }

    // Create success embed
    success(title, description, options = {}) {
        const embed = new EmbedBuilder()
            .setColor(this.colors.success)
            .setTitle(`${this.emojis.getSuccess ? this.emojis.getSuccess() : '✅'} ${title}`)
            .setTimestamp()
            .setFooter({ 
                text: this.branding.footer,
                iconURL: this.branding.logo 
            });

        if (description) embed.setDescription(description);
        if (options.thumbnail) embed.setThumbnail(options.thumbnail);
        if (options.image) embed.setImage(options.image);
        if (options.fields) embed.addFields(options.fields);
        if (options.author) embed.setAuthor(options.author);

        return embed;
    }

    // Create error embed
    error(title, description, options = {}) {
        const embed = new EmbedBuilder()
            .setColor(this.colors.error)
            .setTitle(`${this.emojis.getError ? this.emojis.getError() : '❌'} ${title}`)
            .setTimestamp()
            .setFooter({ 
                text: this.branding.footer,
                iconURL: this.branding.logo 
            });

        if (description) embed.setDescription(description);
        if (options.thumbnail) embed.setThumbnail(options.thumbnail);
        if (options.image) embed.setImage(options.image);
        if (options.fields) embed.addFields(options.fields);
        if (options.author) embed.setAuthor(options.author);

        return embed;
    }

    // Create warning embed
    warning(title, description, options = {}) {
        const embed = new EmbedBuilder()
            .setColor(this.colors.warning)
            .setTitle(`${this.emojis.getWarning ? this.emojis.getWarning() : '⚠️'} ${title}`)
            .setTimestamp()
            .setFooter({ 
                text: this.branding.footer,
                iconURL: this.branding.logo 
            });

        if (description) embed.setDescription(description);
        if (options.thumbnail) embed.setThumbnail(options.thumbnail);
        if (options.image) embed.setImage(options.image);
        if (options.fields) embed.addFields(options.fields);
        if (options.author) embed.setAuthor(options.author);

        return embed;
    }

    // Create info embed
    info(title, description, options = {}) {
        const embed = new EmbedBuilder()
            .setColor(this.colors.info)
            .setTitle(`${this.emojis.getInfo ? this.emojis.getInfo() : 'ℹ️'} ${title}`)
            .setTimestamp()
            .setFooter({ 
                text: this.branding.footer,
                iconURL: this.branding.logo 
            });

        if (description) embed.setDescription(description);
        if (options.thumbnail) embed.setThumbnail(options.thumbnail);
        if (options.image) embed.setImage(options.image);
        if (options.fields) embed.addFields(options.fields);
        if (options.author) embed.setAuthor(options.author);

        return embed;
    }

    // Create primary embed (main brand color)
    primary(title, description, options = {}) {
        const embed = new EmbedBuilder()
            .setColor(this.colors.primary)
            .setTitle(`${this.emojis.getShield ? this.emojis.getShield() : '🛡️'} ${title}`)
            .setTimestamp()
            .setFooter({ 
                text: this.branding.footer,
                iconURL: this.branding.logo 
            });

        if (description) embed.setDescription(description);
        if (options.thumbnail) embed.setThumbnail(options.thumbnail);
        if (options.image) embed.setImage(options.image);
        if (options.fields) embed.addFields(options.fields);
        if (options.author) embed.setAuthor(options.author);

        return embed;
    }

    // Create premium embed
    premium(title, description, options = {}) {
        const embed = new EmbedBuilder()
            .setColor(this.colors.premium)
            .setTitle(`${this.emojis.getPremium ? this.emojis.getPremium() : '👑'} ${title}`)
            .setTimestamp()
            .setFooter({ 
                text: this.branding.footer + " • Premium Feature",
                iconURL: this.branding.logo 
            });

        if (description) embed.setDescription(description);
        if (options.thumbnail) embed.setThumbnail(options.thumbnail);
        if (options.image) embed.setImage(options.image);
        if (options.fields) embed.addFields(options.fields);
        if (options.author) embed.setAuthor(options.author);

        return embed;
    }

    // Create custom embed with full control
    custom(options = {}) {
        const embed = new EmbedBuilder()
            .setColor(options.color || this.colors.primary)
            .setTimestamp()
            .setFooter({ 
                text: options.footer || this.branding.footer,
                iconURL: options.footerIcon || this.branding.logo 
            });

        if (options.title) {
            const emoji = options.emoji || (this.emojis.getShield ? this.emojis.getShield() : '🛡️');
            embed.setTitle(`${emoji} ${options.title}`);
        }
        
        if (options.description) embed.setDescription(options.description);
        if (options.thumbnail) embed.setThumbnail(options.thumbnail);
        if (options.image) embed.setImage(options.image);
        if (options.fields) embed.addFields(options.fields);
        if (options.author) embed.setAuthor(options.author);
        if (options.url) embed.setURL(options.url);

        return embed;
    }

    // Create economy embed
    economy(title, description, options = {}) {
        const embed = new EmbedBuilder()
            .setColor(this.colors.premium)
            .setTitle(`${this.emojis.getCoin ? this.emojis.getCoin() : '💰'} ${title}`)
            .setTimestamp()
            .setFooter({ 
                text: this.branding.footer + " • Economy System",
                iconURL: this.branding.logo 
            });

        if (description) embed.setDescription(description);
        if (options.thumbnail) embed.setThumbnail(options.thumbnail);
        if (options.image) embed.setImage(options.image);
        if (options.fields) embed.addFields(options.fields);
        if (options.author) embed.setAuthor(options.author);

        return embed;
    }

    // Create moderation embed
    moderation(title, description, options = {}) {
        const embed = new EmbedBuilder()
            .setColor(this.colors.dark)
            .setTitle(`${this.emojis.getHammer ? this.emojis.getHammer() : '🔨'} ${title}`)
            .setTimestamp()
            .setFooter({ 
                text: this.branding.footer + " • Moderation System",
                iconURL: this.branding.logo 
            });

        if (description) embed.setDescription(description);
        if (options.thumbnail) embed.setThumbnail(options.thumbnail);
        if (options.image) embed.setImage(options.image);
        if (options.fields) embed.addFields(options.fields);
        if (options.author) embed.setAuthor(options.author);

        return embed;
    }

    // Create ticket embed
    ticket(title, description, options = {}) {
        const embed = new EmbedBuilder()
            .setColor(this.colors.info)
            .setTitle(`${this.emojis.getTicket ? this.emojis.getTicket() : '🎫'} ${title}`)
            .setTimestamp()
            .setFooter({ 
                text: this.branding.footer + " • Ticket System",
                iconURL: this.branding.logo 
            });

        if (description) embed.setDescription(description);
        if (options.thumbnail) embed.setThumbnail(options.thumbnail);
        if (options.image) embed.setImage(options.image);
        if (options.fields) embed.addFields(options.fields);
        if (options.author) embed.setAuthor(options.author);

        return embed;
    }

    // Create welcome embed
    welcome(title, description, options = {}) {
        const embed = new EmbedBuilder()
            .setColor(this.colors.success)
            .setTitle(`${this.emojis.getWelcome ? this.emojis.getWelcome() : '👋'} ${title}`)
            .setTimestamp()
            .setFooter({ 
                text: this.branding.footer + " • Welcome System",
                iconURL: this.branding.logo 
            });

        if (description) embed.setDescription(description);
        if (options.thumbnail) embed.setThumbnail(options.thumbnail);
        if (options.image) embed.setImage(options.image);
        if (options.fields) embed.addFields(options.fields);
        if (options.author) embed.setAuthor(options.author);

        return embed;
    }

    // Get color by name
    getColor(colorName) {
        return this.colors[colorName] || this.colors.primary;
    }

    // Get branding info
    getBranding() {
        return this.branding;
    }
}

module.exports = CustomEmbedBuilder;
