const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const os = require('os');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('botstatus')
        .setDescription('Display detailed bot status and information')
        .setDMPermission(false),

    async execute(interaction) {
        await interaction.deferReply();

        try {
            const client = interaction.client;
            
            // Calculate uptime
            const uptime = process.uptime();
            const days = Math.floor(uptime / 86400);
            const hours = Math.floor((uptime % 86400) / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = Math.floor(uptime % 60);
            const uptimeString = `${days}d ${hours}h ${minutes}m ${seconds}s`;

            // Memory usage
            const memUsage = process.memoryUsage();
            const memUsed = (memUsage.heapUsed / 1024 / 1024).toFixed(2);
            const memTotal = (memUsage.heapTotal / 1024 / 1024).toFixed(2);

            // System info
            const cpuUsage = process.cpuUsage();
            const totalMem = (os.totalmem() / 1024 / 1024 / 1024).toFixed(2);
            const freeMem = (os.freemem() / 1024 / 1024 / 1024).toFixed(2);
            const usedMem = (totalMem - freeMem).toFixed(2);

            // Bot statistics
            const totalGuilds = client.guilds.cache.size;
            const totalUsers = client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);
            const totalChannels = client.channels.cache.size;
            const totalCommands = client.commands ? client.commands.size : 0;

            // Database stats (if available)
            let databaseStatus = 'Not Available';
            if (global.database && global.database.db) {
                databaseStatus = 'Connected & Active';
            }

            // System status
            const systemStats = [
                `${global.emojiReplacer.getComputer()} **Platform:** ${os.platform()} ${os.arch()}`,
                `${global.emojiReplacer.getGear()} **Node.js:** ${process.version}`,
                `${global.emojiReplacer.getChart()} **Memory:** ${memUsed}MB / ${memTotal}MB`,
                `${global.emojiReplacer.getServer()} **System RAM:** ${usedMem}GB / ${totalMem}GB`,
                `${global.emojiReplacer.getTime()} **Uptime:** ${uptimeString}`
            ].join('\n');

            // Bot statistics
            const botStats = [
                `${global.emojiReplacer.getServer()} **Servers:** ${totalGuilds.toLocaleString()}`,
                `${global.emojiReplacer.getUser()} **Users:** ${totalUsers.toLocaleString()}`,
                `${global.emojiReplacer.getList()} **Channels:** ${totalChannels.toLocaleString()}`,
                `${global.emojiReplacer.getGear()} **Commands:** ${totalCommands}`,
                `${global.emojiReplacer.getChart()} **Database:** ${databaseStatus}`
            ].join('\n');

            // Features status
            const featuresStatus = [
                `${global.emojiReplacer.getShield()} **Anti-Spam:** ${global.antiSpamSystem ? 'Active' : 'Inactive'}`,
                `${global.emojiReplacer.getCoin()} **Economy:** ${global.economySystem ? 'Active' : 'Inactive'}`,
                `${global.emojiReplacer.getTicket()} **Tickets:** ${global.advancedTicketSystem ? 'Active' : 'Inactive'}`,
                `${global.emojiReplacer.getReaction()} **Reaction Roles:** ${global.reactionRoleSystem ? 'Active' : 'Inactive'}`,
                `${global.emojiReplacer.getAutoRole()} **Auto Roles:** ${global.autoRoleSystem ? 'Active' : 'Inactive'}`,
                `${global.emojiReplacer.getSparkles()} **Custom Emojis:** ${global.emojiManager ? 'Active' : 'Inactive'}`
            ].join('\n');

            // Performance metrics
            const ping = client.ws.ping;
            let pingStatus = 'Excellent';
            let pingColor = global.embedBuilder.getColor('success');
            
            if (ping > 100) {
                pingStatus = 'Good';
                pingColor = global.embedBuilder.getColor('warning');
            }
            if (ping > 200) {
                pingStatus = 'Poor';
                pingColor = global.embedBuilder.getColor('error');
            }

            const performanceStats = [
                `${global.emojiReplacer.getZap()} **API Latency:** ${ping}ms (${pingStatus})`,
                `${global.emojiReplacer.getRocket()} **Response Time:** <1s`,
                `${global.emojiReplacer.getChart()} **Load:** Normal`,
                `${global.emojiReplacer.getSuccess()} **Status:** Fully Operational`
            ].join('\n');

            // Create beautiful status embed
            const statusEmbed = global.embedBuilder.custom({
                color: pingColor,
                title: 'Bot Status & Information',
                emoji: global.emojiReplacer.getRobot(),
                description: `**${global.embedBuilder.getBranding().name}** is running smoothly and all systems are operational!`,
                fields: [
                    { name: `${global.emojiReplacer.getChart()} System Information`, value: systemStats, inline: true },
                    { name: `${global.emojiReplacer.getServer()} Bot Statistics`, value: botStats, inline: true },
                    { name: `${global.emojiReplacer.getZap()} Performance Metrics`, value: performanceStats, inline: false },
                    { name: `${global.emojiReplacer.getGear()} Active Features`, value: featuresStatus, inline: false },
                    { name: `${global.emojiReplacer.getInfo()} Version Information`, value: `**Bot Version:** 2.0.0\n**Last Updated:** ${new Date().toLocaleDateString()}\n**Developer:** Shanta Somali Team`, inline: false }
                ],
                thumbnail: client.user.displayAvatarURL({ size: 256 }),
                footer: `${global.embedBuilder.getBranding().footer} • Status checked at`
            });

            await interaction.editReply({ embeds: [statusEmbed] });

        } catch (error) {
            console.error('Error in botstatus command:', error);
            const errorEmbed = global.embedBuilder.error(
                'Status Check Failed',
                'An error occurred while retrieving bot status information.',
                {
                    fields: [
                        { name: `${global.emojiReplacer.getInfo()} Support`, value: 'Please contact the bot administrators if this issue persists.', inline: false }
                    ]
                }
            );
            await interaction.editReply({ embeds: [errorEmbed] });
        }
    }
};
