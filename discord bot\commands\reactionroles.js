const { <PERSON>lash<PERSON><PERSON>mandB<PERSON>er, Em<PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('reactionroles')
        .setDescription('Manage reaction roles system')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .setDMPermission(false)
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Create a reaction role message')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Channel to send the reaction role message')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('title')
                        .setDescription('Title for the reaction role message')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('description')
                        .setDescription('Description for the reaction role message')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('roles')
                        .setDescription('Format: emoji1:@role1,emoji2:@role2 (e.g., 🎮:@Gamer,🎵:@Music)')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('add')
                .setDescription('Add a reaction role to existing message')
                .addStringOption(option =>
                    option.setName('message_id')
                        .setDescription('Message ID of the reaction role message')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('emoji')
                        .setDescription('Emoji for the reaction (e.g., 😀 or :custom_emoji:)')
                        .setRequired(true))
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role to assign when reacted')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('remove')
                .setDescription('Remove a reaction role')
                .addStringOption(option =>
                    option.setName('message_id')
                        .setDescription('Message ID of the reaction role message')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('emoji')
                        .setDescription('Emoji to remove')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all reaction roles in this server'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('Delete a reaction role message')
                .addStringOption(option =>
                    option.setName('message_id')
                        .setDescription('Message ID to delete')
                        .setRequired(true))),

    async execute(interaction) {
        const reactionRoleSystem = global.reactionRoleSystem;
        if (!reactionRoleSystem) {
            return await interaction.reply({
                content: '❌ Reaction role system is not available.',
                ephemeral: true
            });
        }

        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'create':
                await this.handleCreate(interaction, reactionRoleSystem);
                break;
            case 'add':
                await this.handleAdd(interaction, reactionRoleSystem);
                break;
            case 'remove':
                await this.handleRemove(interaction, reactionRoleSystem);
                break;
            case 'delete':
                await this.handleDelete(interaction, reactionRoleSystem);
                break;
            case 'list':
                await this.handleList(interaction, reactionRoleSystem);
                break;
        }
    },

    async handleCreate(interaction, reactionRoleSystem) {
        const channel = interaction.options.getChannel('channel');
        const title = interaction.options.getString('title');
        const description = interaction.options.getString('description');
        const rolesString = interaction.options.getString('roles');

        // Validate channel
        if (!channel.isTextBased()) {
            return await interaction.reply({
                content: '❌ Please select a text channel.',
                ephemeral: true
            });
        }

        // Check permissions
        if (!channel.permissionsFor(interaction.guild.members.me).has(PermissionFlagsBits.SendMessages)) {
            return await interaction.reply({
                content: '❌ I don\'t have permission to send messages in that channel.',
                ephemeral: true
            });
        }

        try {
            // Parse roles string
            const roles = new Map();
            const rolePairs = rolesString.split(',');

            for (const pair of rolePairs) {
                const [emoji, roleStr] = pair.split(':');
                if (!emoji || !roleStr) {
                    return await interaction.reply({
                        content: '❌ Invalid format. Use: emoji1:@role1,emoji2:@role2',
                        ephemeral: true
                    });
                }

                // Extract role ID from mention
                const roleId = roleStr.trim().replace(/[<@&>]/g, '');
                const role = interaction.guild.roles.cache.get(roleId);

                if (!role) {
                    return await interaction.reply({
                        content: `❌ Role not found: ${roleStr}`,
                        ephemeral: true
                    });
                }

                // Check if bot can assign this role
                if (interaction.guild.members.me.roles.highest.position <= role.position) {
                    return await interaction.reply({
                        content: `❌ I cannot assign the role **${role.name}** because it's higher than my highest role.`,
                        ephemeral: true
                    });
                }

                roles.set(emoji.trim(), role.id);
            }

            if (roles.size === 0) {
                return await interaction.reply({
                    content: '❌ No valid roles found.',
                    ephemeral: true
                });
            }

            await interaction.deferReply({ ephemeral: true });

            // Create reaction role message
            const message = await reactionRoleSystem.createReactionRoleMessage(
                interaction, channel, title, description, roles
            );

            const emojis = global.emojiReplacer || {};
            await interaction.editReply({
                content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Reaction Role Message Created!**\n\n**Channel:** ${channel}\n**Message ID:** \`${message.id}\`\n**Roles:** ${roles.size}\n\nUsers can now react to get roles!`
            });

        } catch (error) {
            console.error('Error creating reaction role message:', error);
            await interaction.editReply({
                content: '❌ Failed to create reaction role message. Please check my permissions.'
            });
        }
    },

    async handleAdd(interaction, reactionRoleSystem) {
        const messageId = interaction.options.getString('message_id');
        const emoji = interaction.options.getString('emoji');
        const role = interaction.options.getRole('role');

        // Check if role is manageable
        if (role.position >= interaction.guild.members.me.roles.highest.position) {
            return await interaction.reply({
                content: '❌ I cannot manage that role because it\'s higher than my highest role.',
                ephemeral: true
            });
        }

        if (role.managed) {
            return await interaction.reply({
                content: '❌ That role is managed by an integration and cannot be assigned.',
                ephemeral: true
            });
        }

        await interaction.deferReply({ ephemeral: true });

        try {
            // Check if message exists and is a reaction role message
            if (!reactionRoleSystem.isReactionRoleMessage(messageId)) {
                return await interaction.editReply({
                    content: '❌ That message is not a reaction role message. Use `/reactionroles create` first.'
                });
            }

            // Add the role to existing message
            const roles = new Map([[emoji, role.id]]);

            // Find the message
            let message = null;
            for (const channel of interaction.guild.channels.cache.values()) {
                if (channel.isTextBased()) {
                    try {
                        message = await channel.messages.fetch(messageId);
                        break;
                    } catch (error) {
                        // Continue searching
                    }
                }
            }

            if (!message) {
                return await interaction.editReply({
                    content: '❌ Could not find the message.'
                });
            }

            // Add reaction
            await message.react(emoji);

            // Update database
            const reactionRoleData = reactionRoleSystem.reactionRoles.get(messageId);
            reactionRoleData.roles.set(emoji, role.id);

            // Save to database
            Database.prepare(`
                INSERT OR REPLACE INTO reaction_roles
                (guild_id, channel_id, message_id, emoji, role_id)
                VALUES (?, ?, ?, ?, ?)
            `).run(interaction.guild.id, message.channel.id, messageId, emoji, role.id);

            const emojis = global.emojiReplacer || {};
            await interaction.editReply({
                content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Reaction role added!**\n\n**Emoji:** ${emoji}\n**Role:** ${role}\n**Message:** [Jump to message](${message.url})`
            });

        } catch (error) {
            console.error('Error adding reaction role:', error);
            await interaction.editReply({
                content: '❌ Failed to add reaction role. Make sure the emoji is valid.'
            });
        }
    },

    async handleRemove(interaction, reactionRoleSystem) {
        const messageId = interaction.options.getString('message_id');
        const emoji = interaction.options.getString('emoji');

        await interaction.deferReply({ ephemeral: true });

        try {
            // Check if message exists
            if (!reactionRoleSystem.isReactionRoleMessage(messageId)) {
                return await interaction.editReply({
                    content: '❌ That message is not a reaction role message.'
                });
            }

            // Remove from memory
            const reactionRoleData = reactionRoleSystem.reactionRoles.get(messageId);
            if (!reactionRoleData.roles.has(emoji)) {
                return await interaction.editReply({
                    content: '❌ No reaction role found for that emoji.'
                });
            }

            reactionRoleData.roles.delete(emoji);

            // Remove from database
            Database.prepare(`
                DELETE FROM reaction_roles
                WHERE guild_id = ? AND message_id = ? AND emoji = ?
            `).run(interaction.guild.id, messageId, emoji);

            const emojis = global.emojiReplacer || {};
            await interaction.editReply({
                content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Reaction role removed!**\n\n**Emoji:** ${emoji}\n**Message ID:** \`${messageId}\``
            });

        } catch (error) {
            console.error('Error removing reaction role:', error);
            await interaction.editReply({
                content: '❌ Failed to remove reaction role.'
            });
        }
    },

    async handleList(interaction, reactionRoleSystem) {
        await interaction.deferReply({ ephemeral: true });

        try {
            const guildSetups = reactionRoleSystem.getGuildReactionRoles(interaction.guild.id);

            if (guildSetups.length === 0) {
                const emojis = global.emojiReplacer || {};
                return await interaction.editReply({
                    content: `${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} No reaction roles found in this server.\n\nUse \`/reactionroles create\` to create one!`
                });
            }

            const emojis = global.emojiReplacer || {};
            const embed = new EmbedBuilder()
                .setColor(0x00AE86)
                .setTitle(`${emojis.getList ? emojis.getList() : '📋'} Reaction Roles`)
                .setDescription(`Found ${guildSetups.length} reaction role message(s) in this server`)
                .setTimestamp();

            for (const setup of guildSetups) {
                const roleList = setup.roles.map(([emoji, roleId]) => {
                    const role = interaction.guild.roles.cache.get(roleId);
                    return `${emoji} → ${role ? role.toString() : 'Deleted Role'}`;
                }).join('\n');

                const channel = interaction.guild.channels.cache.get(setup.channelId);
                embed.addFields({
                    name: `Message ID: ${setup.messageId}`,
                    value: `**Channel:** ${channel || 'Unknown'}\n**Roles:**\n${roleList || 'No roles'}`,
                    inline: false
                });
            }

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error listing reaction roles:', error);
            await interaction.editReply({
                content: '❌ Failed to list reaction roles.'
            });
        }
    },

    async handleDelete(interaction, reactionRoleSystem) {
        const messageId = interaction.options.getString('message_id');

        await interaction.deferReply({ ephemeral: true });

        try {
            // Check if message exists
            if (!reactionRoleSystem.isReactionRoleMessage(messageId)) {
                return await interaction.editReply({
                    content: '❌ That message is not a reaction role message.'
                });
            }

            // Remove from system
            const removed = await reactionRoleSystem.removeReactionRoleMessage(messageId);

            if (removed) {
                const emojis = global.emojiReplacer || {};
                await interaction.editReply({
                    content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Reaction role message deleted!**\n\nAll reaction roles for message \`${messageId}\` have been removed.`
                });
            } else {
                await interaction.editReply({
                    content: '❌ Failed to delete reaction role message.'
                });
            }

        } catch (error) {
            console.error('Error deleting reaction role message:', error);
            await interaction.editReply({
                content: '❌ Failed to delete reaction role message.'
            });
        }
    }
};
