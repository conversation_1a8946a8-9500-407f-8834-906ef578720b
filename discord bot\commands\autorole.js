const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('autorole')
        .setDescription('Manage auto role system')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .setDMPermission(false)
        .addSubcommand(subcommand =>
            subcommand
                .setName('add')
                .setDescription('Add a role to auto-assign to new members')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role to auto-assign')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('remove')
                .setDescription('Remove a role from auto-assignment')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role to remove from auto-assignment')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all auto-assigned roles'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('toggle')
                .setDescription('Enable or disable the auto role system')
                .addBooleanOption(option =>
                    option.setName('enabled')
                        .setDescription('Enable or disable auto roles')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('settings')
                .setDescription('Configure auto role settings')
                .addIntegerOption(option =>
                    option.setName('delay')
                        .setDescription('Delay in seconds before assigning roles (0 = instant)')
                        .setMinValue(0)
                        .setMaxValue(3600))
                .addStringOption(option =>
                    option.setName('welcome_message')
                        .setDescription('Welcome message to send to new members (use {user}, {server}, {roles})'))
                .addBooleanOption(option =>
                    option.setName('dm_new_members')
                        .setDescription('Send welcome message via DM to new members'))),

    async execute(interaction) {
        const autoRoleSystem = global.autoRoleSystem;
        if (!autoRoleSystem) {
            return await interaction.reply({
                content: '❌ Auto role system is not available.',
                ephemeral: true
            });
        }

        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'add':
                await this.handleAdd(interaction, autoRoleSystem);
                break;
            case 'remove':
                await this.handleRemove(interaction, autoRoleSystem);
                break;
            case 'list':
                await this.handleList(interaction, autoRoleSystem);
                break;
            case 'toggle':
                await this.handleToggle(interaction, autoRoleSystem);
                break;
            case 'settings':
                await this.handleSettings(interaction, autoRoleSystem);
                break;
        }
    },

    async handleAdd(interaction, autoRoleSystem) {
        const role = interaction.options.getRole('role');

        // Check if role is manageable
        if (role.position >= interaction.guild.members.me.roles.highest.position) {
            return await interaction.reply({
                content: '❌ I cannot manage that role because it\'s higher than my highest role.',
                ephemeral: true
            });
        }

        if (role.managed) {
            return await interaction.reply({
                content: '❌ That role is managed by an integration and cannot be auto-assigned.',
                ephemeral: true
            });
        }

        if (role.id === interaction.guild.roles.everyone.id) {
            return await interaction.reply({
                content: '❌ Cannot auto-assign the @everyone role.',
                ephemeral: true
            });
        }

        await interaction.deferReply({ ephemeral: true });

        try {
            const success = await autoRoleSystem.addAutoRole(interaction.guild.id, role.id);

            if (success) {
                const emojis = global.emojiReplacer || {};
                await interaction.editReply({
                    content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Auto Role Added!**\n\nThe role ${role} will now be automatically assigned to new members.`
                });
            } else {
                await interaction.editReply({
                    content: '❌ Failed to add auto role. It may already be added.'
                });
            }

        } catch (error) {
            console.error('Error adding auto role:', error);
            await interaction.editReply({
                content: '❌ Failed to add auto role.'
            });
        }
    },

    async handleRemove(interaction, autoRoleSystem) {
        const role = interaction.options.getRole('role');

        await interaction.deferReply({ ephemeral: true });

        try {
            const success = await autoRoleSystem.removeAutoRole(interaction.guild.id, role.id);

            if (success) {
                const emojis = global.emojiReplacer || {};
                await interaction.editReply({
                    content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Auto Role Removed!**\n\nThe role ${role} will no longer be automatically assigned to new members.`
                });
            } else {
                await interaction.editReply({
                    content: '❌ That role is not in the auto-assign list.'
                });
            }

        } catch (error) {
            console.error('Error removing auto role:', error);
            await interaction.editReply({
                content: '❌ Failed to remove auto role.'
            });
        }
    },

    async handleList(interaction, autoRoleSystem) {
        await interaction.deferReply({ ephemeral: true });

        try {
            const settings = autoRoleSystem.getGuildSettings(interaction.guild.id);
            const emojis = global.emojiReplacer || {};

            if (settings.roles.length === 0) {
                return await interaction.editReply({
                    content: `${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} No auto roles configured.\n\nUse \`/autorole add\` to add roles!`
                });
            }

            const embed = new EmbedBuilder()
                .setColor(0x00AE86)
                .setTitle(`${emojis.getRole ? emojis.getRole() : '🤖'} Auto Role Settings`)
                .setDescription(`Auto role system is **${settings.enabled ? 'Enabled' : 'Disabled'}**`)
                .setTimestamp();

            // Auto roles list
            const roleList = settings.roles.map(roleId => {
                const role = interaction.guild.roles.cache.get(roleId);
                return role ? role.toString() : 'Deleted Role';
            }).join('\n');

            embed.addFields({
                name: `${emojis.getList ? emojis.getList() : '📋'} Auto Roles (${settings.roles.length})`,
                value: roleList || 'None',
                inline: false
            });

            // Settings
            const settingsText = [
                `**Delay:** ${settings.delay} seconds`,
                `**DM New Members:** ${settings.dmNewMembers ? 'Yes' : 'No'}`,
                `**Welcome Message:** ${settings.welcomeMessage ? 'Set' : 'Not set'}`
            ].join('\n');

            embed.addFields({
                name: `${emojis.getSettings ? emojis.getSettings() : '⚙️'} Settings`,
                value: settingsText,
                inline: false
            });

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error listing auto roles:', error);
            await interaction.editReply({
                content: '❌ Failed to list auto roles.'
            });
        }
    },

    async handleToggle(interaction, autoRoleSystem) {
        const enabled = interaction.options.getBoolean('enabled');

        await interaction.deferReply({ ephemeral: true });

        try {
            const success = await autoRoleSystem.toggleSystem(interaction.guild.id, enabled);

            if (success) {
                const emojis = global.emojiReplacer || {};
                await interaction.editReply({
                    content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Auto Role System ${enabled ? 'Enabled' : 'Disabled'}!**\n\n${enabled ? 'New members will now receive auto roles.' : 'New members will no longer receive auto roles.'}`
                });
            } else {
                await interaction.editReply({
                    content: '❌ Failed to toggle auto role system.'
                });
            }

        } catch (error) {
            console.error('Error toggling auto role system:', error);
            await interaction.editReply({
                content: '❌ Failed to toggle auto role system.'
            });
        }
    },

    async handleSettings(interaction, autoRoleSystem) {
        const delay = interaction.options.getInteger('delay');
        const welcomeMessage = interaction.options.getString('welcome_message');
        const dmNewMembers = interaction.options.getBoolean('dm_new_members');

        // If no options provided, show current settings
        if (delay === null && !welcomeMessage && dmNewMembers === null) {
            return await this.handleList(interaction, autoRoleSystem);
        }

        await interaction.deferReply({ ephemeral: true });

        try {
            const currentSettings = autoRoleSystem.getGuildSettings(interaction.guild.id);
            
            const newSettings = {
                enabled: currentSettings.enabled,
                delay: delay !== null ? delay : currentSettings.delay,
                welcomeMessage: welcomeMessage !== null ? welcomeMessage : currentSettings.welcomeMessage,
                dmNewMembers: dmNewMembers !== null ? dmNewMembers : currentSettings.dmNewMembers
            };

            const success = await autoRoleSystem.setSettings(interaction.guild.id, newSettings);

            if (success) {
                const emojis = global.emojiReplacer || {};
                const changes = [];
                
                if (delay !== null) changes.push(`**Delay:** ${delay} seconds`);
                if (welcomeMessage !== null) changes.push(`**Welcome Message:** ${welcomeMessage ? 'Updated' : 'Removed'}`);
                if (dmNewMembers !== null) changes.push(`**DM New Members:** ${dmNewMembers ? 'Enabled' : 'Disabled'}`);

                await interaction.editReply({
                    content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Auto Role Settings Updated!**\n\n${changes.join('\n')}`
                });
            } else {
                await interaction.editReply({
                    content: '❌ Failed to update auto role settings.'
                });
            }

        } catch (error) {
            console.error('Error updating auto role settings:', error);
            await interaction.editReply({
                content: '❌ Failed to update auto role settings.'
            });
        }
    }
};
