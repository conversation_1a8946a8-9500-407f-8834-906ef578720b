const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('autorole')
        .setDescription('Manage auto role system')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .setDMPermission(false)
        .addSubcommand(subcommand =>
            subcommand
                .setName('add')
                .setDescription('Add a role to auto-assign to new members')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role to auto-assign')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('remove')
                .setDescription('Remove a role from auto-assignment')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role to remove from auto-assignment')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all auto-assigned roles'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('toggle')
                .setDescription('Enable or disable the auto role system')
                .addBooleanOption(option =>
                    option.setName('enabled')
                        .setDescription('Enable or disable auto roles')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('settings')
                .setDescription('Configure auto role settings')
                .addIntegerOption(option =>
                    option.setName('delay')
                        .setDescription('Delay in seconds before assigning roles (0 = instant)')
                        .setMinValue(0)
                        .setMaxValue(3600))
                .addStringOption(option =>
                    option.setName('welcome_message')
                        .setDescription('Welcome message to send to new members (use {user}, {server}, {roles})'))
                .addBooleanOption(option =>
                    option.setName('dm_new_members')
                        .setDescription('Send welcome message via DM to new members'))),

    async execute(interaction) {
        const autoRoleSystem = global.autoRoleSystem;
        if (!autoRoleSystem) {
            return await interaction.reply({
                content: '❌ Auto role system is not available.',
                ephemeral: true
            });
        }

        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'add':
                await this.handleAdd(interaction, autoRoleSystem);
                break;
            case 'remove':
                await this.handleRemove(interaction, autoRoleSystem);
                break;
            case 'list':
                await this.handleList(interaction, autoRoleSystem);
                break;
            case 'toggle':
                await this.handleToggle(interaction, autoRoleSystem);
                break;
            case 'settings':
                await this.handleSettings(interaction, autoRoleSystem);
                break;
        }
    },

    async handleAdd(interaction, autoRoleSystem) {
        const role = interaction.options.getRole('role');

        // Check if role is manageable
        if (role.position >= interaction.guild.members.me.roles.highest.position) {
            const errorEmbed = global.embedBuilder.error(
                'Role Hierarchy Error',
                `I cannot manage the role **${role.name}** because it's higher than my highest role.\n\nPlease move my role above **${role.name}** in the server settings.`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        if (role.managed) {
            const errorEmbed = global.embedBuilder.error(
                'Managed Role',
                `The role **${role.name}** is managed by an integration and cannot be auto-assigned.\n\nPlease choose a different role.`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        if (role.id === interaction.guild.roles.everyone.id) {
            const errorEmbed = global.embedBuilder.error(
                'Invalid Role',
                'Cannot auto-assign the @everyone role as it\'s given to all members by default.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        await interaction.deferReply({ ephemeral: true });

        try {
            const success = await autoRoleSystem.addAutoRole(interaction.guild.id, role.id);

            if (success) {
                const successEmbed = global.embedBuilder.success(
                    'Auto Role Added!',
                    `The role **${role.name}** has been successfully added to the auto-assignment list.`,
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getRole()} Role`, value: `${role}`, inline: true },
                            { name: `${global.emojiReplacer.getAutoRole()} Status`, value: 'Active', inline: true },
                            { name: `${global.emojiReplacer.getInfo()} Effect`, value: 'New members will automatically receive this role when they join the server.', inline: false }
                        ]
                    }
                );
                await interaction.editReply({ embeds: [successEmbed] });
            } else {
                const errorEmbed = global.embedBuilder.error(
                    'Failed to Add Auto Role',
                    'The role may already be in the auto-assignment list or there was a database error.'
                );
                await interaction.editReply({ embeds: [errorEmbed] });
            }

        } catch (error) {
            console.error('Error adding auto role:', error);
            const errorEmbed = global.embedBuilder.error(
                'System Error',
                'An unexpected error occurred while adding the auto role. Please try again later.',
                {
                    fields: [
                        { name: `${global.emojiReplacer.getInfo()} Support`, value: 'If this issue persists, please contact the server administrators.', inline: false }
                    ]
                }
            );
            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },

    async handleRemove(interaction, autoRoleSystem) {
        const role = interaction.options.getRole('role');

        await interaction.deferReply({ ephemeral: true });

        try {
            const success = await autoRoleSystem.removeAutoRole(interaction.guild.id, role.id);

            if (success) {
                const emojis = global.emojiReplacer || {};
                await interaction.editReply({
                    content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Auto Role Removed!**\n\nThe role ${role} will no longer be automatically assigned to new members.`
                });
            } else {
                await interaction.editReply({
                    content: '❌ That role is not in the auto-assign list.'
                });
            }

        } catch (error) {
            console.error('Error removing auto role:', error);
            await interaction.editReply({
                content: '❌ Failed to remove auto role.'
            });
        }
    },

    async handleList(interaction, autoRoleSystem) {
        await interaction.deferReply({ ephemeral: true });

        try {
            const settings = autoRoleSystem.getGuildSettings(interaction.guild.id);

            if (settings.roles.length === 0) {
                const infoEmbed = global.embedBuilder.info(
                    'No Auto Roles Configured',
                    'No auto roles have been set up for this server yet.',
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getInfo()} Getting Started`, value: 'Use `/autorole add` to add roles that will be automatically assigned to new members!', inline: false }
                        ]
                    }
                );
                return await interaction.editReply({ embeds: [infoEmbed] });
            }

            // Auto roles list
            const roleList = settings.roles.map(roleId => {
                const role = interaction.guild.roles.cache.get(roleId);
                return role ? `${global.emojiReplacer.getRole()} ${role}` : `${global.emojiReplacer.getError()} Deleted Role`;
            }).join('\n');

            // Settings info
            const settingsInfo = [
                `${global.emojiReplacer.getTime()} **Delay:** ${settings.delay} seconds`,
                `${global.emojiReplacer.getMail()} **DM New Members:** ${settings.dmNewMembers ? 'Yes' : 'No'}`,
                `${global.emojiReplacer.getWelcome()} **Welcome Message:** ${settings.welcomeMessage ? 'Set' : 'Not set'}`
            ].join('\n');

            const statusColor = settings.enabled ? 'success' : 'warning';
            const statusText = settings.enabled ? 'Enabled & Active' : 'Disabled';

            const listEmbed = global.embedBuilder.custom({
                color: global.embedBuilder.getColor(statusColor),
                title: 'Auto Role Settings',
                emoji: global.emojiReplacer.getAutoRole(),
                description: `Auto role system is **${statusText}**`,
                fields: [
                    { name: `${global.emojiReplacer.getList()} Auto Roles (${settings.roles.length})`, value: roleList, inline: false },
                    { name: `${global.emojiReplacer.getSettings()} Configuration`, value: settingsInfo, inline: false },
                    { name: `${global.emojiReplacer.getInfo()} How It Works`, value: 'When new members join the server, they will automatically receive the roles listed above after the configured delay.', inline: false }
                ]
            });

            await interaction.editReply({ embeds: [listEmbed] });

        } catch (error) {
            console.error('Error listing auto roles:', error);
            await interaction.editReply({
                content: '❌ Failed to list auto roles.'
            });
        }
    },

    async handleToggle(interaction, autoRoleSystem) {
        const enabled = interaction.options.getBoolean('enabled');

        await interaction.deferReply({ ephemeral: true });

        try {
            const success = await autoRoleSystem.toggleSystem(interaction.guild.id, enabled);

            if (success) {
                const emojis = global.emojiReplacer || {};
                await interaction.editReply({
                    content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Auto Role System ${enabled ? 'Enabled' : 'Disabled'}!**\n\n${enabled ? 'New members will now receive auto roles.' : 'New members will no longer receive auto roles.'}`
                });
            } else {
                await interaction.editReply({
                    content: '❌ Failed to toggle auto role system.'
                });
            }

        } catch (error) {
            console.error('Error toggling auto role system:', error);
            await interaction.editReply({
                content: '❌ Failed to toggle auto role system.'
            });
        }
    },

    async handleSettings(interaction, autoRoleSystem) {
        const delay = interaction.options.getInteger('delay');
        const welcomeMessage = interaction.options.getString('welcome_message');
        const dmNewMembers = interaction.options.getBoolean('dm_new_members');

        // If no options provided, show current settings
        if (delay === null && !welcomeMessage && dmNewMembers === null) {
            return await this.handleList(interaction, autoRoleSystem);
        }

        await interaction.deferReply({ ephemeral: true });

        try {
            const currentSettings = autoRoleSystem.getGuildSettings(interaction.guild.id);
            
            const newSettings = {
                enabled: currentSettings.enabled,
                delay: delay !== null ? delay : currentSettings.delay,
                welcomeMessage: welcomeMessage !== null ? welcomeMessage : currentSettings.welcomeMessage,
                dmNewMembers: dmNewMembers !== null ? dmNewMembers : currentSettings.dmNewMembers
            };

            const success = await autoRoleSystem.setSettings(interaction.guild.id, newSettings);

            if (success) {
                const emojis = global.emojiReplacer || {};
                const changes = [];
                
                if (delay !== null) changes.push(`**Delay:** ${delay} seconds`);
                if (welcomeMessage !== null) changes.push(`**Welcome Message:** ${welcomeMessage ? 'Updated' : 'Removed'}`);
                if (dmNewMembers !== null) changes.push(`**DM New Members:** ${dmNewMembers ? 'Enabled' : 'Disabled'}`);

                await interaction.editReply({
                    content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Auto Role Settings Updated!**\n\n${changes.join('\n')}`
                });
            } else {
                await interaction.editReply({
                    content: '❌ Failed to update auto role settings.'
                });
            }

        } catch (error) {
            console.error('Error updating auto role settings:', error);
            await interaction.editReply({
                content: '❌ Failed to update auto role settings.'
            });
        }
    }
};
