const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON>er, ButtonB<PERSON>er, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const Database = require('../database/database');

class ReactionRoleSystem {
    constructor(client) {
        this.client = client;
        this.reactionRoles = new Map(); // messageId -> { channelId, guildId, roles: Map(emoji -> roleId) }
        this.setupDatabase();
        this.loadReactionRoles();
    }

    // Setup database tables
    setupDatabase() {
        try {
            // Database tables are already created in main database setup
            // Just log that reaction roles system is ready
            console.log('💾 Reaction Roles system ready (using existing database tables)');

        } catch (error) {
            console.error('Error setting up reaction roles database:', error);
        }
    }

    // Load reaction roles from database
    loadReactionRoles() {
        try {
            // For now, start with empty reaction roles
            // Will be loaded when database is properly connected
            console.log('🎭 Reaction Roles system initialized (will load data when database is ready)');

        } catch (error) {
            console.error('Error loading reaction roles:', error);
        }
    }

    // Create reaction role message
    async createReactionRoleMessage(interaction, channel, title, description, roles) {
        try {
            const emojis = global.emojiReplacer || {};
            
            // Create beautiful embed using the custom embed builder
            const roleList = Array.from(roles.entries()).map(([emoji, roleId]) => {
                const role = interaction.guild.roles.cache.get(roleId);
                return `${emoji} → ${role ? role.toString() : 'Unknown Role'}`;
            }).join('\n');

            const embed = global.embedBuilder.custom({
                color: global.embedBuilder.getColor('primary'),
                title: title,
                emoji: global.emojiReplacer.getReaction(),
                description: description,
                fields: [
                    {
                        name: `${global.emojiReplacer.getRole()} Available Roles`,
                        value: roleList,
                        inline: false
                    },
                    {
                        name: `${global.emojiReplacer.getInfo()} How to Use`,
                        value: `${global.emojiReplacer.getReaction()} Click on the reactions below to get or remove roles\n${global.emojiReplacer.getSparkles()} Changes are applied instantly!`,
                        inline: false
                    }
                ],
                footer: "Powered by Shanta Somali • React to get roles!"
            });

            // Send message
            const message = await channel.send({ embeds: [embed] });

            // Add reactions
            for (const [emoji] of roles) {
                try {
                    await message.react(emoji);
                } catch (error) {
                    console.error(`Failed to add reaction ${emoji}:`, error);
                }
            }

            // Store in database and memory
            for (const [emoji, roleId] of roles) {
                // Store in database using the global database instance
                if (global.database && global.database.db) {
                    global.database.db.run(`
                        INSERT OR REPLACE INTO reaction_roles
                        (guild_id, channel_id, message_id, emoji, role_id)
                        VALUES (?, ?, ?, ?, ?)
                    `, [interaction.guild.id, channel.id, message.id, emoji, roleId]);
                }
            }

            // Store in memory
            this.reactionRoles.set(message.id, {
                channelId: channel.id,
                guildId: interaction.guild.id,
                roles: new Map(roles)
            });

            return message;

        } catch (error) {
            console.error('Error creating reaction role message:', error);
            throw error;
        }
    }

    // Handle reaction add
    async handleReactionAdd(reaction, user) {
        if (user.bot) return;

        try {
            const messageId = reaction.message.id;
            const reactionRoleData = this.reactionRoles.get(messageId);
            
            if (!reactionRoleData) return;

            const emoji = reaction.emoji.name || reaction.emoji.toString();
            const roleId = reactionRoleData.roles.get(emoji);
            
            if (!roleId) return;

            const guild = this.client.guilds.cache.get(reactionRoleData.guildId);
            if (!guild) return;

            const member = await guild.members.fetch(user.id).catch(() => null);
            if (!member) return;

            const role = guild.roles.cache.get(roleId);
            if (!role) return;

            // Check if user already has role
            if (member.roles.cache.has(roleId)) return;

            // Add role
            await member.roles.add(role, 'Reaction role assignment');
            
            console.log(`🎭 REACTION ROLE [${guild.name}] Added role ${role.name} to ${user.tag}`);

            // Send beautiful DM notification (optional)
            try {
                const dmEmbed = global.embedBuilder.success(
                    'Role Added!',
                    `You have been given the **${role.name}** role in **${guild.name}**!`,
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getRole()} Role`, value: `${role}`, inline: true },
                            { name: `${global.emojiReplacer.getServer()} Server`, value: guild.name, inline: true }
                        ]
                    }
                );
                await user.send({ embeds: [dmEmbed] });
            } catch (error) {
                // DM failed, ignore
            }

        } catch (error) {
            console.error('Error handling reaction add:', error);
        }
    }

    // Handle reaction remove
    async handleReactionRemove(reaction, user) {
        if (user.bot) return;

        try {
            const messageId = reaction.message.id;
            const reactionRoleData = this.reactionRoles.get(messageId);
            
            if (!reactionRoleData) return;

            const emoji = reaction.emoji.name || reaction.emoji.toString();
            const roleId = reactionRoleData.roles.get(emoji);
            
            if (!roleId) return;

            const guild = this.client.guilds.cache.get(reactionRoleData.guildId);
            if (!guild) return;

            const member = await guild.members.fetch(user.id).catch(() => null);
            if (!member) return;

            const role = guild.roles.cache.get(roleId);
            if (!role) return;

            // Check if user has role
            if (!member.roles.cache.has(roleId)) return;

            // Remove role
            await member.roles.remove(role, 'Reaction role removal');
            
            console.log(`🎭 REACTION ROLE [${guild.name}] Removed role ${role.name} from ${user.tag}`);

            // Send beautiful DM notification (optional)
            try {
                const dmEmbed = global.embedBuilder.warning(
                    'Role Removed',
                    `The **${role.name}** role has been removed from you in **${guild.name}**.`,
                    {
                        fields: [
                            { name: `${global.emojiReplacer.getRole()} Role`, value: role.name, inline: true },
                            { name: `${global.emojiReplacer.getServer()} Server`, value: guild.name, inline: true }
                        ]
                    }
                );
                await user.send({ embeds: [dmEmbed] });
            } catch (error) {
                // DM failed, ignore
            }

        } catch (error) {
            console.error('Error handling reaction remove:', error);
        }
    }

    // Remove reaction role setup
    async removeReactionRoleMessage(messageId) {
        try {
            // Remove from database
            if (global.database && global.database.db) {
                global.database.db.run(`
                    DELETE FROM reaction_roles WHERE message_id = ?
                `, [messageId]);
            }

            // Remove from memory
            this.reactionRoles.delete(messageId);

            console.log(`🗑️ Removed reaction role setup for message ${messageId}`);
            return true;
        } catch (error) {
            console.error('Error removing reaction role message:', error);
            return false;
        }
    }

    // Get reaction role setups for guild
    getGuildReactionRoles(guildId) {
        const guildSetups = [];
        
        for (const [messageId, data] of this.reactionRoles) {
            if (data.guildId === guildId) {
                guildSetups.push({
                    messageId,
                    channelId: data.channelId,
                    roles: Array.from(data.roles.entries())
                });
            }
        }
        
        return guildSetups;
    }

    // Check if message is reaction role message
    isReactionRoleMessage(messageId) {
        return this.reactionRoles.has(messageId);
    }
}

module.exports = ReactionRoleSystem;
