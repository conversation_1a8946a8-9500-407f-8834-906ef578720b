// Utility to replace all default emojis with custom emojis throughout the bot

class EmojiReplacer {
    constructor() {
        // Mapping of default emojis to custom emoji names
        this.emojiMap = {
            // Error and success
            '❌': 'error',
            '✅': 'success', 
            '⚠️': 'warning',
            'ℹ️': 'info',
            
            // Status indicators
            '🟢': 'online',
            '🔴': 'offline', 
            '🟡': 'idle',
            
            // Actions
            '🚫': 'icon37',
            '👤': 'icon5',
            '🛡️': 'shield',
            '📄': 'icon',
            '⏰': 'icon3',
            '📅': 'icon20',
            '💬': 'icon7',
            '🔨': 'hammer',
            '👢': 'boot',
            '🔇': 'mute',
            '🗑️': 'delete',
            
            // System
            '🤖': 'robot',
            '⚙️': 'gear',
            '🔧': 'wrench',
            '📝': 'memo',
            '📊': 'chart',
            '🎫': 'ticket',
            '🔍': 'mag',
            '🏓': 'ping',
            '💻': 'computer',
            '🏠': 'server',
            '🆔': 'icon',
            '🌐': 'server',
            '📷': 'icon',
            '📛': 'icon',
            '🔗': 'icon',
            '🕒': 'icon3',
            
            // Moderation
            '🚀': 'rocket',
            '📦': 'icon',
            '🔐': 'locked',
            '🎉': 'success',
            '👥': 'icon5',
            '⚡': 'zap',
            '🔄': 'arrows',
            '👋': 'icon',
            '🔘': 'icon',
            '📁': 'folder',
            '🧹': 'delete',
            '📌': 'icon',
            '🔒': 'locked',
            '🔓': 'unlocked',
            '📡': 'computer',
            '📢': 'icon',
            '⏳': 'icon3',
            '⏱️': 'icon3',
            '👑': 'crown',
            '📈': 'chart',
            '🏆': 'success',
            '🏅': 'success',
            '⭐': 'success',
            '🔥': 'success',
            '📥': 'icon',
            '📤': 'icon',
            '🔔': 'icon',

            // New additions for enhanced system
            '🎭': 'icon',
            '💰': 'success',
            '💎': 'success',
            '🎮': 'icon',
            '🎵': 'icon',
            '📚': 'icon',
            '✨': 'success',
            '🎁': 'success',
            '📧': 'icon',
            '📞': 'icon',
            '🌍': 'server',
            '🏳️': 'icon',
            '🇸🇴': 'icon'
        };
    }

    // Get custom emoji or fallback
    getCustomEmoji(defaultEmoji) {
        if (!global.emojiManager) {
            return defaultEmoji;
        }

        const customEmojiName = this.emojiMap[defaultEmoji];
        if (customEmojiName) {
            return global.emojiManager.get(customEmojiName) || defaultEmoji;
        }

        return defaultEmoji;
    }

    // Replace emojis in text
    replaceEmojisInText(text) {
        if (!text || typeof text !== 'string') {
            return text;
        }

        let replacedText = text;
        for (const [defaultEmoji, customEmojiName] of Object.entries(this.emojiMap)) {
            if (replacedText.includes(defaultEmoji)) {
                const customEmoji = global.emojiManager ? 
                    global.emojiManager.get(customEmojiName) || defaultEmoji : 
                    defaultEmoji;
                replacedText = replacedText.replace(new RegExp(defaultEmoji, 'g'), customEmoji);
            }
        }

        return replacedText;
    }

    // Get emoji by name with fallback
    get(name, fallback = '❓') {
        if (!global.emojiManager) {
            return fallback;
        }
        return global.emojiManager.get(name) || fallback;
    }

    // Common emoji getters
    getError() { return this.get('error', '❌'); }
    getSuccess() { return this.get('success', '✅'); }
    getWarning() { return this.get('warning', '⚠️'); }
    getInfo() { return this.get('info', 'ℹ️'); }
    getOnline() { return this.get('online', '🟢'); }
    getOffline() { return this.get('offline', '🔴'); }
    getBan() { return this.get('icon37', '🚫'); }
    getUser() { return this.get('icon5', '👤'); }
    getAdmin() { return this.get('shield', '🛡️'); }
    getReason() { return this.get('icon', '📄'); }
    getTime() { return this.get('icon3', '⏰'); }
    getDate() { return this.get('icon20', '📅'); }
    getMessage() { return this.get('icon7', '💬'); }
    getPing() { return this.get('ping', '🏓'); }
    getRobot() { return this.get('robot', '🤖'); }
    getTicket() { return this.get('ticket', '🎫'); }
    getChart() { return this.get('chart', '📊'); }
    getGear() { return this.get('gear', '⚙️'); }
    getServer() { return this.get('server', '🏠'); }
    getComputer() { return this.get('computer', '💻'); }

    // New enhanced emoji getters
    getShield() { return this.get('shield', '🛡️'); }
    getHammer() { return this.get('hammer', '🔨'); }
    getBoot() { return this.get('boot', '👢'); }
    getMute() { return this.get('mute', '🔇'); }
    getDelete() { return this.get('delete', '🗑️'); }
    getRocket() { return this.get('rocket', '🚀'); }
    getCrown() { return this.get('crown', '👑'); }
    getZap() { return this.get('zap', '⚡'); }
    getArrows() { return this.get('arrows', '🔄'); }
    getFolder() { return this.get('folder', '📁'); }
    getLocked() { return this.get('locked', '🔒'); }
    getUnlocked() { return this.get('unlocked', '🔓'); }

    // System specific emojis
    getRole() { return this.get('icon', '🎭'); }
    getReaction() { return this.get('icon', '🎭'); }
    getAutoRole() { return this.get('robot', '🤖'); }
    getCoin() { return this.get('success', '💰'); }
    getGem() { return this.get('success', '💎'); }
    getGame() { return this.get('icon', '🎮'); }
    getMusic() { return this.get('icon', '🎵'); }
    getBook() { return this.get('icon', '📚'); }
    getSparkles() { return this.get('success', '✨'); }
    getGift() { return this.get('success', '🎁'); }
    getMail() { return this.get('icon', '📧'); }
    getPhone() { return this.get('icon', '📞'); }
    getGlobe() { return this.get('server', '🌍'); }
    getFlag() { return this.get('icon', '🏳️'); }
    getSomalia() { return this.get('icon', '🇸🇴'); }
    getWelcome() { return this.get('icon', '👋'); }
    getPremium() { return this.get('crown', '👑'); }
    getSettings() { return this.get('gear', '⚙️'); }
    getList() { return this.get('icon', '📋'); }
    getTrophy() { return this.get('success', '🏆'); }
    getMedal() { return this.get('success', '🏅'); }
    getStar() { return this.get('success', '⭐'); }
    getFire() { return this.get('success', '🔥'); }
    getParty() { return this.get('success', '🎉'); }
    getBell() { return this.get('icon', '🔔'); }
}

// Create global instance
const emojiReplacer = new EmojiReplacer();

module.exports = emojiReplacer;
